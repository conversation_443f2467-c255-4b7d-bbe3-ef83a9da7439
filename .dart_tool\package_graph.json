{"roots": ["travel_offers"], "packages": [{"name": "travel_offers", "version": "2.5.0+28", "dependencies": ["android_intent_plus", "any_link_preview", "audioplayers", "awesome_notifications", "cached_network_image", "change_app_package_name", "connectivity_plus", "country_picker", "cupertino_icons", "device_info_plus", "device_preview", "device_region", "dio", "dotted_border", "encrypt", "file_picker", "firebase_auth", "firebase_core", "firebase_messaging", "flick_video_player", "flutter", "flutter_bloc", "flutter_image_compress", "flutter_localizations", "flutter_pdfview", "flutter_stripe", "flutter_svg", "flutter_widget_from_html", "fluttertoast", "google_cloud_translation", "google_mobile_ads", "google_sign_in", "hive", "hive_flutter", "image_cropper", "image_picker", "in_app_purchase", "in_app_review", "lottie", "mime_type", "open_filex", "package_info_plus", "path", "path_provider", "permission_handler", "phonepe_payment_sdk", "qr_code_scanner", "qr_flutter", "razorpay_flutter", "record", "share_plus", "shimmer", "sign_in_with_apple", "sms_autofill", "url_launcher", "vibration", "video_player", "webview_flutter", "youtube_player_flutter"], "devDependencies": ["flutter_lints", "flutter_test", "integration_test", "rename"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "integration_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "file", "flutter", "flutter_driver", "flutter_test", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "sync_http", "term_glyph", "test_api", "vector_math", "vm_service", "webdriver"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "rename", "version": "3.0.2", "dependencies": ["args", "logger", "path"]}, {"name": "fluttertoast", "version": "8.2.12", "dependencies": ["flutter", "flutter_web_plugins", "web"]}, {"name": "phonepe_payment_sdk", "version": "2.0.3", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "google_cloud_translation", "version": "0.0.4", "dependencies": ["flutter", "google_api_headers", "html_unescape", "http"]}, {"name": "android_intent_plus", "version": "5.3.0", "dependencies": ["flutter", "meta", "platform"]}, {"name": "file_picker", "version": "9.2.3", "dependencies": ["cross_file", "ffi", "flutter", "flutter_plugin_android_lifecycle", "flutter_web_plugins", "path", "plugin_platform_interface", "web", "win32"]}, {"name": "flutter_widget_from_html", "version": "0.16.0", "dependencies": ["flutter", "flutter_widget_from_html_core", "fwfh_cached_network_image", "fwfh_chewie", "fwfh_just_audio", "fwfh_svg", "fwfh_url_launcher", "fwfh_webview", "html"]}, {"name": "webview_flutter", "version": "4.10.0", "dependencies": ["flutter", "webview_flutter_android", "webview_flutter_platform_interface", "webview_flutter_wkwebview"]}, {"name": "sms_autofill", "version": "2.4.1", "dependencies": ["flutter", "pin_input_text_field"]}, {"name": "device_info_plus", "version": "11.3.3", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "razorpay_flutter", "version": "1.4.0", "dependencies": ["eventify", "flutter", "fluttertoast"]}, {"name": "in_app_purchase", "version": "3.2.1", "dependencies": ["flutter", "in_app_purchase_android", "in_app_purchase_platform_interface", "in_app_purchase_storekit"]}, {"name": "flutter_stripe", "version": "11.4.0", "dependencies": ["flutter", "meta", "stripe_android", "stripe_ios", "stripe_platform_interface"]}, {"name": "qr_code_scanner", "version": "1.0.1", "dependencies": ["flutter", "flutter_web_plugins", "js"]}, {"name": "qr_flutter", "version": "4.1.0", "dependencies": ["flutter", "qr"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "video_player", "version": "2.9.5", "dependencies": ["flutter", "html", "video_player_android", "video_player_avfoundation", "video_player_platform_interface", "video_player_web"]}, {"name": "change_app_package_name", "version": "1.5.0", "dependencies": []}, {"name": "flutter_pdfview", "version": "1.4.0", "dependencies": ["flutter"]}, {"name": "device_region", "version": "1.4.0", "dependencies": ["flutter"]}, {"name": "vibration", "version": "3.1.3", "dependencies": ["flutter", "plugin_platform_interface", "vibration_platform_interface"]}, {"name": "flutter_image_compress", "version": "2.4.0", "dependencies": ["flutter", "flutter_image_compress_common", "flutter_image_compress_macos", "flutter_image_compress_ohos", "flutter_image_compress_platform_interface", "flutter_image_compress_web"]}, {"name": "google_mobile_ads", "version": "5.3.1", "dependencies": ["flutter", "meta", "webview_flutter", "webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "flutter_svg", "version": "2.0.17", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "sign_in_with_apple", "version": "6.1.4", "dependencies": ["flutter", "meta", "sign_in_with_apple_platform_interface", "sign_in_with_apple_web"]}, {"name": "google_sign_in", "version": "6.3.0", "dependencies": ["flutter", "google_sign_in_android", "google_sign_in_ios", "google_sign_in_platform_interface", "google_sign_in_web"]}, {"name": "encrypt", "version": "5.0.3", "dependencies": ["args", "asn1lib", "clock", "collection", "crypto", "pointycastle"]}, {"name": "any_link_preview", "version": "3.0.3", "dependencies": ["flutter", "flutter_svg", "html", "http", "shared_preferences", "string_validator", "url_launcher", "xml"]}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "open_filex", "version": "4.7.0", "dependencies": ["ffi", "flutter"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "image_cropper", "version": "9.1.0", "dependencies": ["flutter", "image_cropper_for_web", "image_cropper_platform_interface"]}, {"name": "audioplayers", "version": "6.4.0", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_platform_interface", "audioplayers_web", "audioplayers_windows", "file", "flutter", "http", "meta", "path_provider", "synchronized", "uuid"]}, {"name": "record", "version": "5.2.1", "dependencies": ["flutter", "record_android", "record_darwin", "record_linux", "record_platform_interface", "record_web", "record_windows", "uuid"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "device_preview", "version": "1.2.0", "dependencies": ["collection", "device_frame", "flutter", "flutter_localizations", "freezed_annotation", "json_annotation", "provider", "shared_preferences"]}, {"name": "awesome_notifications", "version": "0.10.1", "dependencies": ["flutter", "flutter_web_plugins", "intl", "plugin_platform_interface"]}, {"name": "mime_type", "version": "1.0.1", "dependencies": []}, {"name": "lottie", "version": "3.3.1", "dependencies": ["archive", "flutter", "http", "path", "vector_math"]}, {"name": "in_app_review", "version": "2.0.10", "dependencies": ["flutter", "in_app_review_platform_interface"]}, {"name": "share_plus", "version": "10.1.4", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "web", "win32"]}, {"name": "url_launcher", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "dotted_border", "version": "2.1.0", "dependencies": ["flutter", "path_drawing"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "shimmer", "version": "3.0.0", "dependencies": ["flutter"]}, {"name": "flutter_bloc", "version": "9.1.0", "dependencies": ["bloc", "flutter", "provider"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "dio", "version": "5.8.0+1", "dependencies": ["async", "collection", "dio_web_adapter", "http_parser", "meta", "path"]}, {"name": "hive_flutter", "version": "1.1.0", "dependencies": ["flutter", "hive", "path", "path_provider"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "connectivity_plus", "version": "6.1.3", "dependencies": ["collection", "connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "meta", "nm", "web"]}, {"name": "country_picker", "version": "2.0.27", "dependencies": ["collection", "flutter", "universal_io"]}, {"name": "youtube_player_flutter", "version": "9.1.1", "dependencies": ["flutter", "flutter_inappwebview"]}, {"name": "flick_video_player", "version": "0.9.0", "dependencies": ["dio", "flutter", "path_provider", "provider", "universal_html", "video_player", "wakelock_plus"]}, {"name": "firebase_messaging", "version": "15.2.5", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "firebase_auth", "version": "5.5.2", "dependencies": ["firebase_auth_platform_interface", "firebase_auth_web", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_core", "version": "3.13.0", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "webdriver", "version": "3.1.0", "dependencies": ["matcher", "path", "stack_trace", "sync_http"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "sync_http", "version": "0.3.1", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "flutter_driver", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "file", "flutter", "flutter_test", "fuchsia_remote_debug_protocol", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "platform", "process", "source_span", "stack_trace", "stream_channel", "string_scanner", "sync_http", "term_glyph", "test_api", "vector_math", "vm_service", "webdriver"]}, {"name": "logger", "version": "2.5.0", "dependencies": []}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "google_api_headers", "version": "4.4.3", "dependencies": ["flutter", "package_info_plus"]}, {"name": "html_unescape", "version": "2.0.0", "dependencies": []}, {"name": "http", "version": "1.3.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "win32", "version": "5.12.0", "dependencies": ["ffi"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.27", "dependencies": ["flutter"]}, {"name": "html", "version": "0.15.5+1", "dependencies": ["csslib", "source_span"]}, {"name": "fwfh_webview", "version": "0.15.4", "dependencies": ["flutter", "flutter_widget_from_html_core", "logging", "web", "webview_flutter", "webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "fwfh_url_launcher", "version": "0.16.0", "dependencies": ["flutter", "flutter_widget_from_html_core", "url_launcher"]}, {"name": "fwfh_svg", "version": "0.16.0", "dependencies": ["flutter", "flutter_svg", "flutter_widget_from_html_core"]}, {"name": "fwfh_just_audio", "version": "0.16.0", "dependencies": ["flutter", "flutter_widget_from_html_core", "just_audio"]}, {"name": "fwfh_chewie", "version": "0.16.0", "dependencies": ["chewie", "flutter", "flutter_widget_from_html_core", "video_player"]}, {"name": "fwfh_cached_network_image", "version": "0.16.0", "dependencies": ["cached_network_image", "flutter", "flutter_cache_manager", "flutter_widget_from_html_core"]}, {"name": "flutter_widget_from_html_core", "version": "0.16.0", "dependencies": ["csslib", "flutter", "html", "logging"]}, {"name": "webview_flutter_wkwebview", "version": "3.18.5", "dependencies": ["flutter", "path", "webview_flutter_platform_interface"]}, {"name": "webview_flutter_platform_interface", "version": "2.10.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "webview_flutter_android", "version": "4.3.4", "dependencies": ["flutter", "webview_flutter_platform_interface"]}, {"name": "pin_input_text_field", "version": "4.5.2", "dependencies": ["flutter"]}, {"name": "win32_registry", "version": "2.1.0", "dependencies": ["ffi", "meta", "win32"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.2", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "eventify", "version": "1.0.1", "dependencies": []}, {"name": "in_app_purchase_storekit", "version": "0.3.21", "dependencies": ["collection", "flutter", "in_app_purchase_platform_interface", "json_annotation"]}, {"name": "in_app_purchase_platform_interface", "version": "1.4.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "in_app_purchase_android", "version": "0.4.0+1", "dependencies": ["collection", "flutter", "in_app_purchase_platform_interface"]}, {"name": "stripe_platform_interface", "version": "11.4.0", "dependencies": ["flutter", "freezed_annotation", "json_annotation", "meta", "plugin_platform_interface"]}, {"name": "stripe_ios", "version": "11.4.0", "dependencies": ["flutter"]}, {"name": "stripe_android", "version": "11.4.0", "dependencies": ["flutter"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "qr", "version": "3.0.2", "dependencies": ["meta"]}, {"name": "video_player_web", "version": "2.3.4", "dependencies": ["flutter", "flutter_web_plugins", "video_player_platform_interface", "web"]}, {"name": "video_player_platform_interface", "version": "6.3.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "video_player_avfoundation", "version": "2.7.0", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "video_player_android", "version": "2.8.2", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "vibration_platform_interface", "version": "0.1.0", "dependencies": ["device_info_plus", "flutter", "plugin_platform_interface"]}, {"name": "flutter_image_compress_ohos", "version": "0.0.3", "dependencies": ["flutter", "flutter_image_compress_platform_interface"]}, {"name": "flutter_image_compress_macos", "version": "1.0.3", "dependencies": ["flutter", "flutter_image_compress_platform_interface"]}, {"name": "flutter_image_compress_web", "version": "0.1.5", "dependencies": ["flutter", "flutter_image_compress_platform_interface", "flutter_web_plugins", "web"]}, {"name": "flutter_image_compress_common", "version": "1.0.6", "dependencies": ["flutter", "flutter_image_compress_platform_interface"]}, {"name": "flutter_image_compress_platform_interface", "version": "1.0.5", "dependencies": ["cross_file", "flutter", "plugin_platform_interface"]}, {"name": "vector_graphics_compiler", "version": "1.1.16", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.18", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "sign_in_with_apple_web", "version": "2.1.1", "dependencies": ["flutter", "flutter_web_plugins", "sign_in_with_apple_platform_interface"]}, {"name": "sign_in_with_apple_platform_interface", "version": "1.1.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "google_sign_in_web", "version": "0.12.4+4", "dependencies": ["flutter", "flutter_web_plugins", "google_identity_services_web", "google_sign_in_platform_interface", "http", "web"]}, {"name": "google_sign_in_platform_interface", "version": "2.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "google_sign_in_ios", "version": "5.8.1", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "google_sign_in_android", "version": "6.2.0", "dependencies": ["flutter", "google_sign_in_platform_interface"]}, {"name": "pointycastle", "version": "3.9.1", "dependencies": ["collection", "convert", "js"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "asn1lib", "version": "1.6.2", "dependencies": []}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "string_validator", "version": "1.1.0", "dependencies": []}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "image_cropper_for_web", "version": "6.1.0", "dependencies": ["flutter", "flutter_web_plugins", "image_cropper_platform_interface", "web"]}, {"name": "image_cropper_platform_interface", "version": "7.1.0", "dependencies": ["flutter", "http", "plugin_platform_interface"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "audioplayers_windows", "version": "4.2.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_web", "version": "5.1.0", "dependencies": ["audioplayers_platform_interface", "flutter", "flutter_web_plugins", "web"]}, {"name": "audioplayers_platform_interface", "version": "7.1.0", "dependencies": ["collection", "flutter", "meta", "plugin_platform_interface"]}, {"name": "audioplayers_linux", "version": "4.2.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_darwin", "version": "6.2.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_android", "version": "5.2.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "record_darwin", "version": "1.2.2", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_android", "version": "1.3.1", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_linux", "version": "0.7.2", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_windows", "version": "1.0.5", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_web", "version": "1.1.6", "dependencies": ["flutter", "flutter_web_plugins", "record_platform_interface", "web"]}, {"name": "record_platform_interface", "version": "1.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.16", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "freezed_annotation", "version": "2.4.4", "dependencies": ["collection", "json_annotation", "meta"]}, {"name": "device_frame", "version": "1.2.0", "dependencies": ["flutter", "freezed_annotation"]}, {"name": "provider", "version": "6.1.4", "dependencies": ["collection", "flutter", "nested"]}, {"name": "archive", "version": "4.0.5", "dependencies": ["crypto", "path", "posix"]}, {"name": "in_app_review_platform_interface", "version": "2.0.5", "dependencies": ["flutter", "platform", "plugin_platform_interface", "url_launcher"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.0", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "share_plus_platform_interface", "version": "5.0.2", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.15", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "path_drawing", "version": "1.0.1", "dependencies": ["flutter", "meta", "path_parsing", "vector_math"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "bloc", "version": "9.0.0", "dependencies": ["meta"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+22", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "dio_web_adapter", "version": "2.1.1", "dependencies": ["dio", "http_parser", "meta", "web"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "connectivity_plus_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "universal_io", "version": "2.2.2", "dependencies": ["collection", "meta", "typed_data"]}, {"name": "flutter_inappwebview", "version": "6.1.5", "dependencies": ["flutter", "flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_platform_interface", "flutter_inappwebview_web", "flutter_inappwebview_windows"]}, {"name": "universal_html", "version": "2.2.4", "dependencies": ["async", "charcode", "collection", "csslib", "html", "meta", "source_span", "typed_data", "universal_io"]}, {"name": "wakelock_plus", "version": "1.2.11", "dependencies": ["dbus", "flutter", "flutter_web_plugins", "meta", "package_info_plus", "wakelock_plus_platform_interface", "web", "win32"]}, {"name": "firebase_messaging_web", "version": "3.10.5", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.6.5", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "5.4.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_auth_web", "version": "5.14.2", "dependencies": ["firebase_auth_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "http_parser", "meta", "web"]}, {"name": "firebase_auth_platform_interface", "version": "7.6.2", "dependencies": ["_flutterfire_internals", "collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.22.0", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "process", "version": "5.0.3", "dependencies": ["file", "path", "platform"]}, {"name": "fuchsia_remote_debug_protocol", "version": "0.0.0", "dependencies": ["file", "meta", "path", "platform", "process", "vm_service"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "just_audio", "version": "0.9.46", "dependencies": ["async", "audio_session", "crypto", "flutter", "just_audio_platform_interface", "just_audio_web", "meta", "path", "path_provider", "rxdart", "uuid"]}, {"name": "chewie", "version": "1.11.1", "dependencies": ["cupertino_icons", "flutter", "provider", "video_player", "wakelock_plus"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "google_identity_services_web", "version": "0.3.3", "dependencies": ["meta", "web"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.9", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "posix", "version": "6.0.1", "dependencies": ["ffi", "meta", "path"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "file_selector_macos", "version": "0.9.4+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "flutter_inappwebview_windows", "version": "0.6.0", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_web", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface", "flutter_web_plugins", "web"]}, {"name": "flutter_inappwebview_macos", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_ios", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_android", "version": "1.1.3", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_platform_interface", "version": "1.3.0+1", "dependencies": ["flutter", "flutter_inappwebview_internal_annotations", "plugin_platform_interface"]}, {"name": "charcode", "version": "1.4.0", "dependencies": []}, {"name": "wakelock_plus_platform_interface", "version": "1.2.2", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "_flutterfire_internals", "version": "1.3.54", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "audio_session", "version": "0.1.25", "dependencies": ["flutter", "flutter_web_plugins", "meta", "rxdart"]}, {"name": "just_audio_web", "version": "0.4.15", "dependencies": ["flutter", "flutter_web_plugins", "just_audio_platform_interface", "web"]}, {"name": "just_audio_platform_interface", "version": "4.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "flutter_inappwebview_internal_annotations", "version": "1.2.0", "dependencies": []}], "configVersion": 1}