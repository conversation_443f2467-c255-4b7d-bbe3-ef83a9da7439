<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Airport extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'address',
        'city',
        'country',
        'location_id',
        'description',
        'map_lat',
        'map_lng',
        'map_zoom',
        'timezone',
        'status',
        'create_user',
        'update_user'
    ];

    protected $appends = ['full_name'];

    /**
     * Get full airport name with code
     */
    public function getFullNameAttribute()
    {
        return $this->name . ' (' . $this->code . ')';
    }

    /**
     * Get departure flights
     */
    public function departureFlights()
    {
        return $this->hasMany(Flight::class, 'airport_from');
    }

    /**
     * Get arrival flights
     */
    public function arrivalFlights()
    {
        return $this->hasMany(Flight::class, 'airport_to');
    }

    /**
     * Get all flights (departure + arrival)
     */
    public function allFlights()
    {
        return Flight::where('airport_from', $this->id)
                    ->orWhere('airport_to', $this->id);
    }

    /**
     * Scope for active airports
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'publish');
    }

    /**
     * Scope by country
     */
    public function scopeByCountry($query, $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope by city
     */
    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }

    /**
     * Get creator user
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    /**
     * Get updater user
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
