import 'package:flutter/material.dart';
import 'package:travel_offers/data/model/flight/flight_model.dart';

class FlightDetailsScreen extends StatefulWidget {
  final FlightModel flight;

  const FlightDetailsScreen({
    Key? key,
    required this.flight,
  }) : super(key: key);

  @override
  State<FlightDetailsScreen> createState() => _FlightDetailsScreenState();
}

class _FlightDetailsScreenState extends State<FlightDetailsScreen> {
  bool _isLoading = false;
  List<FlightSeatModel> _availableSeats = [];
  FlightSeatModel? _selectedSeat;

  @override
  void initState() {
    super.initState();
    _loadSeats();
  }

  Future<void> _loadSeats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load available seats for this flight
      // final seats = await FlightService.getFlightSeats(widget.flight.id);
      // setState(() {
      //   _availableSeats = seats;
      //   _isLoading = false;
      // });
      
      // Mock data for now
      await Future.delayed(Duration(seconds: 1));
      setState(() {
        _availableSeats = _getMockSeats();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل المقاعد')),
      );
    }
  }

  List<FlightSeatModel> _getMockSeats() {
    return List.generate(20, (index) {
      return FlightSeatModel(
        id: index + 1,
        seatNumber: '${(index ~/ 6) + 1}${String.fromCharCode(65 + (index % 6))}',
        seatType: index < 6 ? 'first' : (index < 12 ? 'business' : 'economy'),
        price: index < 6 ? 450.0 : (index < 12 ? 300.0 : 150.0),
        personType: 'adult',
        isAvailable: index % 3 != 0, // Some seats are booked
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل الرحلة ${widget.flight.flightNumber}'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Flight Info Card
            _buildFlightInfoCard(),
            
            SizedBox(height: 16),
            
            // Route Info Card
            _buildRouteInfoCard(),
            
            SizedBox(height: 16),
            
            // Pricing Card
            _buildPricingCard(),
            
            SizedBox(height: 16),
            
            // Seat Selection
            _buildSeatSelection(),
            
            SizedBox(height: 24),
            
            // Book Button
            _buildBookButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildFlightInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flight, color: Theme.of(context).primaryColor),
                SizedBox(width: 8),
                Text(
                  'معلومات الرحلة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('رقم الرحلة', style: TextStyle(color: Colors.grey[600])),
                      Text(
                        widget.flight.flightNumber,
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('شركة الطيران', style: TextStyle(color: Colors.grey[600])),
                      Text(
                        widget.flight.airline.name,
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('نوع الطائرة', style: TextStyle(color: Colors.grey[600])),
                      Text(
                        widget.flight.aircraftType,
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('المدة', style: TextStyle(color: Colors.grey[600])),
                      Text(
                        '${widget.flight.duration} دقيقة',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRouteInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.flight_takeoff, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'مسار الرحلة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            
            Row(
              children: [
                // Departure
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        widget.flight.departureAirport.code,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      Text(
                        widget.flight.departureAirport.city,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '${widget.flight.departureTime.hour.toString().padLeft(2, '0')}:${widget.flight.departureTime.minute.toString().padLeft(2, '0')}',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        '${widget.flight.departureTime.day}/${widget.flight.departureTime.month}/${widget.flight.departureTime.year}',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                
                // Arrow
                Column(
                  children: [
                    Icon(Icons.arrow_forward, color: Theme.of(context).primaryColor),
                    Text(
                      '${widget.flight.duration} د',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
                
                // Arrival
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        widget.flight.arrivalAirport.code,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      Text(
                        widget.flight.arrivalAirport.city,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '${widget.flight.arrivalTime.hour.toString().padLeft(2, '0')}:${widget.flight.arrivalTime.minute.toString().padLeft(2, '0')}',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        '${widget.flight.arrivalTime.day}/${widget.flight.arrivalTime.month}/${widget.flight.arrivalTime.year}',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'الأسعار',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('السعر الأساسي:', style: TextStyle(fontSize: 16)),
                Text(
                  '${widget.flight.minPrice.toStringAsFixed(3)} د.ك',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            
            if (_selectedSeat != null) ...[
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('المقعد المحدد:', style: TextStyle(fontSize: 16)),
                  Text(
                    '${_selectedSeat!.price.toStringAsFixed(3)} د.ك',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSeatSelection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.airline_seat_recline_normal, color: Theme.of(context).primaryColor),
                SizedBox(width: 8),
                Text(
                  'اختيار المقعد',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            
            if (_isLoading)
              Center(child: CircularProgressIndicator())
            else
              _buildSeatMap(),
          ],
        ),
      ),
    );
  }

  Widget _buildSeatMap() {
    return Column(
      children: [
        // Legend
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildLegendItem(Colors.green, 'متاح'),
            _buildLegendItem(Colors.red, 'محجوز'),
            _buildLegendItem(Colors.blue, 'محدد'),
          ],
        ),
        SizedBox(height: 16),
        
        // Seat Grid
        GridView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 6,
            childAspectRatio: 1,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
          ),
          itemCount: _availableSeats.length,
          itemBuilder: (context, index) {
            final seat = _availableSeats[index];
            final isSelected = _selectedSeat?.id == seat.id;
            
            return GestureDetector(
              onTap: seat.isAvailable ? () {
                setState(() {
                  _selectedSeat = isSelected ? null : seat;
                });
              } : null,
              child: Container(
                decoration: BoxDecoration(
                  color: !seat.isAvailable 
                      ? Colors.red 
                      : isSelected 
                          ? Colors.blue 
                          : Colors.green,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    seat.seatNumber ?? '',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        SizedBox(width: 4),
        Text(label, style: TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildBookButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _selectedSeat != null ? _bookFlight : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          padding: EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          _selectedSeat != null 
              ? 'احجز الآن - ${_selectedSeat!.price.toStringAsFixed(3)} د.ك'
              : 'اختر مقعد للحجز',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  void _bookFlight() {
    if (_selectedSeat == null) return;
    
    // Navigate to booking screen
    Navigator.pushNamed(
      context,
      '/flight-booking',
      arguments: {
        'flight': widget.flight,
        'seat': _selectedSeat,
      },
    );
  }
}


