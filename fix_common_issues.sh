#!/bin/bash

echo "🔧 بدء إصلاح المشاكل الشائعة..."

# الانتقال إلى مجلد panel
cd panel

echo "📦 تثبيت مكتبة QR Code..."
composer require simplesoftwareio/simple-qrcode

echo "🔧 إصلاح صلاحيات الملفات..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod -R 644 storage/logs/laravel.log 2>/dev/null || true

echo "🗂️ إنشاء مجلدات مطلوبة..."
mkdir -p storage/app/public/qr_codes/tickets
mkdir -p storage/app/public/airlines
mkdir -p storage/app/public/events
mkdir -p public/storage

echo "🔗 إنشاء symbolic link للتخزين..."
php artisan storage:link

echo "🧹 مسح cache قديم..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo "📊 إنشاء جداول قاعدة البيانات..."
php artisan migrate --force

echo "🌱 إضافة البيانات التجريبية..."
php artisan db:seed --class=FlightSystemSeeder --force
php artisan db:seed --class=EventSystemSeeder --force

echo "🔄 تحديث autoloader..."
composer dump-autoload

echo "📱 العودة لإصلاح Flutter..."
cd ..

echo "🧹 مسح cache Flutter..."
flutter clean
flutter pub get

echo "🔧 إصلاح مشاكل Flutter الشائعة..."
flutter pub deps
flutter pub upgrade

echo "✅ انتهى إصلاح المشاكل!"
echo ""
echo "📋 ما تم إصلاحه:"
echo "   ✓ تثبيت مكتبة QR Code"
echo "   ✓ إصلاح صلاحيات الملفات"
echo "   ✓ إنشاء المجلدات المطلوبة"
echo "   ✓ إنشاء symbolic links"
echo "   ✓ مسح جميع أنواع cache"
echo "   ✓ تشغيل migrations"
echo "   ✓ إضافة البيانات التجريبية"
echo "   ✓ تحديث autoloader"
echo "   ✓ إصلاح مشاكل Flutter"
echo ""
echo "🎉 المشروع جاهز للاستخدام!"
