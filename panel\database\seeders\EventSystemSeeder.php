<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Venue;
use App\Models\Event;
use App\Models\TicketCategory;
use Carbon\Carbon;

class EventSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create venues
        $venues = [
            [
                'name' => 'Jaber Al-Ahmad International Stadium',
                'name_ar' => 'استاد جابر الأحمد الدولي',
                'address' => 'South Surra, Kuwait City',
                'city' => 'Kuwait City',
                'country' => 'Kuwait',
                'capacity' => 60000,
                'facilities' => json_encode(['VIP Boxes', 'Parking', 'Food Courts', 'Medical Center']),
                'status' => 'active'
            ],
            [
                'name' => 'Kuwait Sports Club Stadium',
                'name_ar' => 'استاد النادي الكويتي الرياضي',
                'address' => 'Kaifan, Kuwait City',
                'city' => 'Kuwait City',
                'country' => 'Kuwait',
                'capacity' => 18500,
                'facilities' => json_encode(['VIP Lounge', 'Parking', 'Cafeteria']),
                'status' => 'active'
            ],
            [
                'name' => 'Al-Sadaqua Walsalam Stadium',
                'name_ar' => 'استاد الصداقة والسلام',
                'address' => 'Adailiya, Kuwait City',
                'city' => 'Kuwait City',
                'country' => 'Kuwait',
                'capacity' => 21500,
                'facilities' => json_encode(['Press Center', 'VIP Areas', 'Player Facilities']),
                'status' => 'active'
            ],
            [
                'name' => 'Sheikh Jaber Al-Ahmad Cultural Centre',
                'name_ar' => 'مركز الشيخ جابر الأحمد الثقافي',
                'address' => 'Kuwait City',
                'city' => 'Kuwait City',
                'country' => 'Kuwait',
                'capacity' => 2500,
                'facilities' => json_encode(['Concert Hall', 'Theater', 'Exhibition Halls']),
                'status' => 'active'
            ]
        ];

        foreach ($venues as $venue) {
            Venue::create(array_merge($venue, ['create_user' => 1]));
        }

        // Create events
        $jaberStadium = Venue::where('name', 'Jaber Al-Ahmad International Stadium')->first();
        $kuwaitStadium = Venue::where('name', 'Kuwait Sports Club Stadium')->first();
        $sadaquaStadium = Venue::where('name', 'Al-Sadaqua Walsalam Stadium')->first();
        $culturalCentre = Venue::where('name', 'Sheikh Jaber Al-Ahmad Cultural Centre')->first();

        $events = [
            [
                'title' => 'Kuwait vs Saudi Arabia - World Cup Qualifier',
                'title_ar' => 'الكويت ضد السعودية - تصفيات كأس العالم',
                'content' => 'Exciting World Cup qualifier match between Kuwait and Saudi Arabia national teams.',
                'content_ar' => 'مباراة مثيرة في تصفيات كأس العالم بين منتخبي الكويت والسعودية.',
                'venue_id' => $jaberStadium->id,
                'event_type' => 'football',
                'organizer' => 'Kuwait Football Association',
                'duration' => 120,
                'start_time' => '20:00',
                'price' => 25.00,
                'sale_price' => 20.00,
                'is_featured' => true,
                'status' => 'upcoming'
            ],
            [
                'title' => 'Al-Kuwait vs Al-Arabi - Kuwait Premier League',
                'title_ar' => 'الكويت ضد العربي - الدوري الكويتي الممتاز',
                'content' => 'Classic derby match in the Kuwait Premier League.',
                'content_ar' => 'مباراة الديربي الكلاسيكية في الدوري الكويتي الممتاز.',
                'venue_id' => $kuwaitStadium->id,
                'event_type' => 'football',
                'organizer' => 'Kuwait Premier League',
                'duration' => 105,
                'start_time' => '18:30',
                'price' => 15.00,
                'is_featured' => false,
                'status' => 'upcoming'
            ],
            [
                'title' => 'Kuwait Basketball Championship Final',
                'title_ar' => 'نهائي بطولة الكويت لكرة السلة',
                'content' => 'Final match of the Kuwait Basketball Championship.',
                'content_ar' => 'المباراة النهائية لبطولة الكويت لكرة السلة.',
                'venue_id' => $sadaquaStadium->id,
                'event_type' => 'basketball',
                'organizer' => 'Kuwait Basketball Association',
                'duration' => 90,
                'start_time' => '19:00',
                'price' => 12.00,
                'is_featured' => false,
                'status' => 'upcoming'
            ],
            [
                'title' => 'Arabic Music Concert - Fairuz Tribute',
                'title_ar' => 'حفل موسيقي عربي - تكريم فيروز',
                'content' => 'Special tribute concert celebrating the legendary Fairuz.',
                'content_ar' => 'حفل تكريمي خاص للاحتفال بالأسطورة فيروز.',
                'venue_id' => $culturalCentre->id,
                'event_type' => 'concert',
                'organizer' => 'Kuwait Cultural Foundation',
                'duration' => 150,
                'start_time' => '20:30',
                'price' => 50.00,
                'sale_price' => 40.00,
                'is_featured' => true,
                'status' => 'upcoming'
            ]
        ];

        foreach ($events as $eventData) {
            $event = Event::create(array_merge($eventData, [
                'slug' => \Illuminate\Support\Str::slug($eventData['title']),
                'create_user' => 1
            ]));

            // Create ticket categories for each event
            if ($event->event_type === 'football') {
                $ticketCategories = [
                    [
                        'name' => 'General Admission',
                        'name_ar' => 'دخول عام',
                        'price' => $event->sale_price ?: $event->price,
                        'total_quantity' => 30000,
                        'available_quantity' => 30000,
                        'description' => 'General seating areas',
                        'description_ar' => 'مناطق الجلوس العامة',
                        'section' => 'General',
                        'is_vip' => false
                    ],
                    [
                        'name' => 'VIP Seats',
                        'name_ar' => 'مقاعد VIP',
                        'price' => ($event->sale_price ?: $event->price) * 3,
                        'total_quantity' => 2000,
                        'available_quantity' => 2000,
                        'description' => 'Premium VIP seating with exclusive amenities',
                        'description_ar' => 'مقاعد VIP مميزة مع خدمات حصرية',
                        'section' => 'VIP',
                        'benefits' => json_encode(['Premium seating', 'Complimentary refreshments', 'VIP parking']),
                        'is_vip' => true
                    ],
                    [
                        'name' => 'Family Section',
                        'name_ar' => 'قسم العائلات',
                        'price' => ($event->sale_price ?: $event->price) * 1.5,
                        'total_quantity' => 5000,
                        'available_quantity' => 5000,
                        'description' => 'Family-friendly seating area',
                        'description_ar' => 'منطقة جلوس مناسبة للعائلات',
                        'section' => 'Family',
                        'is_vip' => false
                    ]
                ];
            } elseif ($event->event_type === 'basketball') {
                $ticketCategories = [
                    [
                        'name' => 'Court Side',
                        'name_ar' => 'جانب الملعب',
                        'price' => ($event->sale_price ?: $event->price) * 4,
                        'total_quantity' => 100,
                        'available_quantity' => 100,
                        'description' => 'Premium court side seating',
                        'description_ar' => 'مقاعد مميزة بجانب الملعب',
                        'section' => 'Court',
                        'is_vip' => true
                    ],
                    [
                        'name' => 'Lower Bowl',
                        'name_ar' => 'المدرج السفلي',
                        'price' => $event->sale_price ?: $event->price,
                        'total_quantity' => 3000,
                        'available_quantity' => 3000,
                        'description' => 'Lower level seating',
                        'description_ar' => 'مقاعد المستوى السفلي',
                        'section' => 'Lower',
                        'is_vip' => false
                    ]
                ];
            } elseif ($event->event_type === 'concert') {
                $ticketCategories = [
                    [
                        'name' => 'Orchestra',
                        'name_ar' => 'الأوركسترا',
                        'price' => ($event->sale_price ?: $event->price) * 2,
                        'total_quantity' => 500,
                        'available_quantity' => 500,
                        'description' => 'Premium orchestra seating',
                        'description_ar' => 'مقاعد الأوركسترا المميزة',
                        'section' => 'Orchestra',
                        'is_vip' => true
                    ],
                    [
                        'name' => 'Balcony',
                        'name_ar' => 'الشرفة',
                        'price' => $event->sale_price ?: $event->price,
                        'total_quantity' => 1000,
                        'available_quantity' => 1000,
                        'description' => 'Balcony seating with great view',
                        'description_ar' => 'مقاعد الشرفة مع إطلالة رائعة',
                        'section' => 'Balcony',
                        'is_vip' => false
                    ]
                ];
            }

            foreach ($ticketCategories as $categoryData) {
                TicketCategory::create(array_merge($categoryData, [
                    'event_id' => $event->id,
                    'create_user' => 1
                ]));
            }
        }
    }
}
