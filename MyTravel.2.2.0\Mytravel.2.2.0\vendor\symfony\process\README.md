Process Component
=================

The Process component executes commands in sub-processes.

Sponsor
-------

The Process component for Symfony 5.4/6.0 is [backed][1] by [SensioLabs][2].

As the creator of Symfony, SensioLabs supports companies using Symfony, with an
offering encompassing consultancy, expertise, services, training, and technical
assistance to ensure the success of web application development projects.

Help Symfony by [sponsoring][3] its development!

Resources
---------

 * [Documentation](https://symfony.com/doc/current/components/process.html)
 * [Contributing](https://symfony.com/doc/current/contributing/index.html)
 * [Report issues](https://github.com/symfony/symfony/issues) and
   [send Pull Requests](https://github.com/symfony/symfony/pulls)
   in the [main Symfony repository](https://github.com/symfony/symfony)

[1]: https://symfony.com/backers
[2]: https://sensiolabs.com
[3]: https://symfony.com/sponsor
