<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventTranslation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'origin_id',
        'locale',
        'title',
        'content',
        'faqs',
        'address',
        'create_user',
        'update_user'
    ];

    protected $casts = [
        'faqs' => 'array'
    ];

    public function event()
    {
        return $this->belongsTo(Event::class, 'origin_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
