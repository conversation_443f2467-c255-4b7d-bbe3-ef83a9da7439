#!/bin/bash

echo "🔧 إصلاح ملفات الموقع الجغرافي..."

# إنشاء ملفات بديلة مبسطة للملفات المعطلة
echo "📁 إنشاء ملفات بديلة..."

# إنشاء ملف بديل لـ nearby_location.dart
cat > lib/ui/screens/location/nearby_location_simple.dart << 'EOF'
import 'package:flutter/material.dart';
import 'package:travel_offers/ui/theme/theme.dart';
import 'package:travel_offers/utils/extensions/extensions.dart';

class NearbyLocationScreen extends StatefulWidget {
  const NearbyLocationScreen({Key? key}) : super(key: key);

  @override
  State<NearbyLocationScreen> createState() => NearbyLocationScreenState();
}

class NearbyLocationScreenState extends State<NearbyLocationScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('المواقع القريبة'),
        backgroundColor: context.color.territoryColor,
        elevation: 0,
        iconTheme: IconThemeData(color: context.color.textColorDark),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 100,
              color: Colors.grey[400],
            ),
            SizedBox(height: 20),
            Text(
              'خدمة المواقع القريبة غير متاحة حالياً',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 30),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.color.territoryColor,
                padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
              child: Text(
                'العودة',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
EOF

# إنشاء ملف بديل لـ location_permission_screen.dart
cat > lib/ui/screens/location_permission_screen_simple.dart << 'EOF'
import 'package:flutter/material.dart';
import 'package:travel_offers/ui/theme/theme.dart';
import 'package:travel_offers/utils/extensions/extensions.dart';

class LocationPermissionScreen extends StatefulWidget {
  const LocationPermissionScreen({Key? key}) : super(key: key);

  @override
  State<LocationPermissionScreen> createState() => LocationPermissionScreenState();
}

class LocationPermissionScreenState extends State<LocationPermissionScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('صلاحيات الموقع'),
        backgroundColor: context.color.territoryColor,
        elevation: 0,
        iconTheme: IconThemeData(color: context.color.textColorDark),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_disabled,
              size: 100,
              color: Colors.grey[400],
            ),
            SizedBox(height: 20),
            Text(
              'خدمات الموقع الجغرافي معطلة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 10),
            Text(
              'لا يمكن الوصول إلى موقعك الحالي',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
            SizedBox(height: 30),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.color.territoryColor,
                padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
              child: Text(
                'العودة',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
EOF

echo "🔄 استبدال الملفات..."

# نسخ احتياطية
mv lib/ui/screens/location/nearby_location.dart lib/ui/screens/location/nearby_location_old.dart 2>/dev/null || true
mv lib/ui/screens/location_permission_screen.dart lib/ui/screens/location_permission_screen_old.dart 2>/dev/null || true

# استبدال بالملفات الجديدة
mv lib/ui/screens/location/nearby_location_simple.dart lib/ui/screens/location/nearby_location.dart
mv lib/ui/screens/location_permission_screen_simple.dart lib/ui/screens/location_permission_screen.dart

echo "✅ تم إصلاح ملفات الموقع الجغرافي!"
