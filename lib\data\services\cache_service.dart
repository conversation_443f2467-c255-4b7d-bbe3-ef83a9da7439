import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CacheService {
  static SharedPreferences? _prefs;
  static const Duration _defaultCacheDuration = Duration(hours: 1);
  
  // Cache keys
  static const String _flightsKey = 'cached_flights';
  static const String _eventsKey = 'cached_events';
  static const String _weatherKey = 'cached_weather';
  static const String _prayerTimesKey = 'cached_prayer_times';
  static const String _holidaysKey = 'cached_holidays';
  static const String _userPreferencesKey = 'user_preferences';
  static const String _searchHistoryKey = 'search_history';
  static const String _favoritesKey = 'favorites';
  
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('CacheService not initialized. Call CacheService.init() first.');
    }
    return _prefs!;
  }

  // Generic cache methods
  static Future<void> setCache(
    String key, 
    dynamic data, {
    Duration? duration,
  }) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'duration': (duration ?? _defaultCacheDuration).inMilliseconds,
    };
    
    await prefs.setString(key, json.encode(cacheData));
  }

  static T? getCache<T>(String key) {
    try {
      final cacheString = prefs.getString(key);
      if (cacheString == null) return null;
      
      final cacheData = json.decode(cacheString);
      final timestamp = cacheData['timestamp'] as int;
      final duration = cacheData['duration'] as int;
      
      // Check if cache is expired
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - timestamp > duration) {
        clearCache(key);
        return null;
      }
      
      return cacheData['data'] as T;
    } catch (e) {
      return null;
    }
  }

  static Future<void> clearCache(String key) async {
    await prefs.remove(key);
  }

  static Future<void> clearAllCache() async {
    final keys = prefs.getKeys().where((key) => key.startsWith('cached_'));
    for (final key in keys) {
      await prefs.remove(key);
    }
  }

  static bool isCacheValid(String key) {
    try {
      final cacheString = prefs.getString(key);
      if (cacheString == null) return false;
      
      final cacheData = json.decode(cacheString);
      final timestamp = cacheData['timestamp'] as int;
      final duration = cacheData['duration'] as int;
      
      final now = DateTime.now().millisecondsSinceEpoch;
      return now - timestamp <= duration;
    } catch (e) {
      return false;
    }
  }

  // Specific cache methods for different data types
  
  // Flights cache
  static Future<void> cacheFlights(List<Map<String, dynamic>> flights) async {
    await setCache(_flightsKey, flights, duration: Duration(minutes: 30));
  }

  static List<Map<String, dynamic>>? getCachedFlights() {
    final cached = getCache<List<dynamic>>(_flightsKey);
    return cached?.cast<Map<String, dynamic>>();
  }

  // Events cache
  static Future<void> cacheEvents(List<Map<String, dynamic>> events) async {
    await setCache(_eventsKey, events, duration: Duration(hours: 2));
  }

  static List<Map<String, dynamic>>? getCachedEvents() {
    final cached = getCache<List<dynamic>>(_eventsKey);
    return cached?.cast<Map<String, dynamic>>();
  }

  // Weather cache
  static Future<void> cacheWeather(Map<String, dynamic> weather) async {
    await setCache(_weatherKey, weather, duration: Duration(minutes: 15));
  }

  static Map<String, dynamic>? getCachedWeather() {
    return getCache<Map<String, dynamic>>(_weatherKey);
  }

  // Prayer times cache
  static Future<void> cachePrayerTimes(Map<String, dynamic> prayerTimes) async {
    await setCache(_prayerTimesKey, prayerTimes, duration: Duration(hours: 24));
  }

  static Map<String, dynamic>? getCachedPrayerTimes() {
    return getCache<Map<String, dynamic>>(_prayerTimesKey);
  }

  // Holidays cache
  static Future<void> cacheHolidays(List<Map<String, dynamic>> holidays) async {
    await setCache(_holidaysKey, holidays, duration: Duration(days: 7));
  }

  static List<Map<String, dynamic>>? getCachedHolidays() {
    final cached = getCache<List<dynamic>>(_holidaysKey);
    return cached?.cast<Map<String, dynamic>>();
  }

  // User preferences
  static Future<void> setUserPreference(String key, dynamic value) async {
    final preferences = getUserPreferences();
    preferences[key] = value;
    await prefs.setString(_userPreferencesKey, json.encode(preferences));
  }

  static T? getUserPreference<T>(String key) {
    final preferences = getUserPreferences();
    return preferences[key] as T?;
  }

  static Map<String, dynamic> getUserPreferences() {
    try {
      final prefsString = prefs.getString(_userPreferencesKey);
      if (prefsString == null) return {};
      return json.decode(prefsString) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }

  // Search history
  static Future<void> addSearchHistory(String query, String type) async {
    final history = getSearchHistory();
    final searchItem = {
      'query': query,
      'type': type,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    
    // Remove duplicate if exists
    history.removeWhere((item) => 
        item['query'] == query && item['type'] == type);
    
    // Add to beginning
    history.insert(0, searchItem);
    
    // Keep only last 20 searches
    if (history.length > 20) {
      history.removeRange(20, history.length);
    }
    
    await prefs.setString(_searchHistoryKey, json.encode(history));
  }

  static List<Map<String, dynamic>> getSearchHistory() {
    try {
      final historyString = prefs.getString(_searchHistoryKey);
      if (historyString == null) return [];
      final history = json.decode(historyString) as List<dynamic>;
      return history.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  static Future<void> clearSearchHistory() async {
    await prefs.remove(_searchHistoryKey);
  }

  // Favorites
  static Future<void> addFavorite(String id, String type, Map<String, dynamic> data) async {
    final favorites = getFavorites();
    final favoriteItem = {
      'id': id,
      'type': type,
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    
    // Remove if already exists
    favorites.removeWhere((item) => item['id'] == id && item['type'] == type);
    
    // Add to beginning
    favorites.insert(0, favoriteItem);
    
    await prefs.setString(_favoritesKey, json.encode(favorites));
  }

  static Future<void> removeFavorite(String id, String type) async {
    final favorites = getFavorites();
    favorites.removeWhere((item) => item['id'] == id && item['type'] == type);
    await prefs.setString(_favoritesKey, json.encode(favorites));
  }

  static List<Map<String, dynamic>> getFavorites() {
    try {
      final favoritesString = prefs.getString(_favoritesKey);
      if (favoritesString == null) return [];
      final favorites = json.decode(favoritesString) as List<dynamic>;
      return favorites.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  static bool isFavorite(String id, String type) {
    final favorites = getFavorites();
    return favorites.any((item) => item['id'] == id && item['type'] == type);
  }

  // App settings
  static Future<void> setLanguage(String languageCode) async {
    await setUserPreference('language', languageCode);
  }

  static String getLanguage() {
    return getUserPreference<String>('language') ?? 'ar';
  }

  static Future<void> setThemeMode(String themeMode) async {
    await setUserPreference('theme_mode', themeMode);
  }

  static String getThemeMode() {
    return getUserPreference<String>('theme_mode') ?? 'system';
  }

  static Future<void> setNotificationsEnabled(bool enabled) async {
    await setUserPreference('notifications_enabled', enabled);
  }

  static bool getNotificationsEnabled() {
    return getUserPreference<bool>('notifications_enabled') ?? true;
  }

  // Cache statistics
  static Map<String, dynamic> getCacheStatistics() {
    final keys = prefs.getKeys().where((key) => key.startsWith('cached_'));
    final stats = <String, dynamic>{};
    
    for (final key in keys) {
      try {
        final cacheString = prefs.getString(key);
        if (cacheString != null) {
          final cacheData = json.decode(cacheString);
          final timestamp = cacheData['timestamp'] as int;
          final duration = cacheData['duration'] as int;
          final now = DateTime.now().millisecondsSinceEpoch;
          final isValid = now - timestamp <= duration;
          
          stats[key] = {
            'size': cacheString.length,
            'timestamp': timestamp,
            'duration': duration,
            'isValid': isValid,
            'expiresIn': isValid ? duration - (now - timestamp) : 0,
          };
        }
      } catch (e) {
        // Skip invalid cache entries
      }
    }
    
    return stats;
  }

  // Cleanup expired cache
  static Future<void> cleanupExpiredCache() async {
    final keys = prefs.getKeys().where((key) => key.startsWith('cached_'));
    
    for (final key in keys) {
      if (!isCacheValid(key)) {
        await prefs.remove(key);
      }
    }
  }

  // Get cache size
  static int getCacheSize() {
    final keys = prefs.getKeys().where((key) => key.startsWith('cached_'));
    int totalSize = 0;
    
    for (final key in keys) {
      final value = prefs.getString(key);
      if (value != null) {
        totalSize += value.length;
      }
    }
    
    return totalSize;
  }
}
