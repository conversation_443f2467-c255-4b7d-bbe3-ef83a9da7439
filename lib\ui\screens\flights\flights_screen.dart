import 'package:flutter/material.dart';
import 'package:travel_offers/data/model/flight/flight_model.dart';
import 'package:travel_offers/ui/widgets/flight_card.dart';

class FlightsScreen extends StatefulWidget {
  const FlightsScreen({Key? key}) : super(key: key);

  @override
  State<FlightsScreen> createState() => _FlightsScreenState();
}

class _FlightsScreenState extends State<FlightsScreen> {
  List<FlightModel> flights = [];
  bool isLoading = true;
  String? selectedFromAirport;
  String? selectedToAirport;
  DateTime? selectedDate;
  String? selectedAirline;

  @override
  void initState() {
    super.initState();
    _loadFlights();
  }

  Future<void> _loadFlights() async {
    setState(() {
      isLoading = true;
    });

    try {
      // : Implement API call to load flights
      // final response = await FlightService.getFlights(filters);
      // setState(() {
      //   flights = response.data;
      //   isLoading = false;
      // });
      
      // Mock data for now
      await Future.delayed(Duration(seconds: 2));
      setState(() {
        flights = _getMockFlights();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل الرحلات')),
      );
    }
  }

  List<FlightModel> _getMockFlights() {
    return [
      FlightModel(
        id: 1,
        title: 'Kuwait to Dubai',
        code: 'FL001',
        flightNumber: 'KU671',
        departureTime: DateTime.now().add(Duration(days: 7)),
        arrivalTime: DateTime.now().add(Duration(days: 7, hours: 1, minutes: 30)),
        duration: 90,
        minPrice: 150.0,
        airline: AirlineModel(
          id: 1,
          name: 'Kuwait Airways',
          code: 'KU',
          country: 'Kuwait',
          status: 'active',
        ),
        departureAirport: AirportModel(
          id: 1,
          name: 'Kuwait International Airport',
          code: 'KWI',
          city: 'Kuwait City',
          country: 'Kuwait',
        ),
        arrivalAirport: AirportModel(
          id: 2,
          name: 'Dubai International Airport',
          code: 'DXB',
          city: 'Dubai',
          country: 'UAE',
        ),
        aircraftType: 'Airbus A320',
        status: 'scheduled',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('الرحلات الجوية'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildAirportSelector(
                        'من',
                        selectedFromAirport,
                        (value) => setState(() => selectedFromAirport = value),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: _buildAirportSelector(
                        'إلى',
                        selectedToAirport,
                        (value) => setState(() => selectedToAirport = value),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildDateSelector(),
                    ),
                    SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: _loadFlights,
                      child: Text('بحث'),
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Flights List
          Expanded(
            child: isLoading
                ? Center(child: CircularProgressIndicator())
                : flights.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.flight_takeoff, size: 64, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد رحلات متاحة',
                              style: TextStyle(fontSize: 18, color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: EdgeInsets.all(16),
                        itemCount: flights.length,
                        itemBuilder: (context, index) {
                          return FlightCard(
                            flight: flights[index],
                            onTap: () => _navigateToFlightDetails(flights[index]),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildAirportSelector(String label, String? value, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(fontWeight: FontWeight.bold)),
        SizedBox(height: 8),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              hint: Text('اختر المطار'),
              isExpanded: true,
              items: [
                DropdownMenuItem(value: 'KWI', child: Text('الكويت (KWI)')),
                DropdownMenuItem(value: 'DXB', child: Text('دبي (DXB)')),
                DropdownMenuItem(value: 'DOH', child: Text('الدوحة (DOH)')),
                DropdownMenuItem(value: 'RUH', child: Text('الرياض (RUH)')),
              ],
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('تاريخ السفر', style: TextStyle(fontWeight: FontWeight.bold)),
        SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: selectedDate ?? DateTime.now().add(Duration(days: 1)),
              firstDate: DateTime.now(),
              lastDate: DateTime.now().add(Duration(days: 365)),
            );
            if (date != null) {
              setState(() => selectedDate = date);
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today, color: Colors.grey),
                SizedBox(width: 8),
                Text(
                  selectedDate != null
                      ? '${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
                      : 'اختر التاريخ',
                  style: TextStyle(
                    color: selectedDate != null ? Colors.black : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToFlightDetails(FlightModel flight) {
    Navigator.pushNamed(
      context,
      '/flight-details',
      arguments: flight,
    );
  }
}
