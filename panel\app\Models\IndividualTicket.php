<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IndividualTicket extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_booking_id',
        'ticket_number',
        'qr_code',
        'seat_number',
        'status',
        'used_at',
        'used_by'
    ];

    protected $casts = [
        'used_at' => 'datetime'
    ];

    public function ticketBooking()
    {
        return $this->belongsTo(TicketBooking::class);
    }

    public function usedByUser()
    {
        return $this->belongsTo(User::class, 'used_by');
    }

    public function markAsUsed($userId = null)
    {
        $this->update([
            'status' => 'used',
            'used_at' => now(),
            'used_by' => $userId ?: auth()->id()
        ]);
    }

    public function isUsed()
    {
        return $this->status === 'used';
    }

    public function isActive()
    {
        return $this->status === 'active';
    }
}
