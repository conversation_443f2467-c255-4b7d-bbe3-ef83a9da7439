import React, { useState, useEffect } from 'react';
import { Card, Form, Button, Row, Col, Alert } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import Select from 'react-select';
import "react-datepicker/dist/react-datepicker.css";

const FlightSearch = ({ onSearch, loading }) => {
  const [searchData, setSearchData] = useState({
    fromAirport: null,
    toAirport: null,
    departureDate: new Date(),
    passengers: 1,
    seatClass: 'economy'
  });

  const [airports, setAirports] = useState([]);
  const [airlines, setAirlines] = useState([]);

  useEffect(() => {
    loadAirports();
    loadAirlines();
  }, []);

  const loadAirports = async () => {
    try {
      const response = await fetch('/api/flights/airports/list');
      const data = await response.json();
      if (data.success) {
        const airportOptions = data.data.map(airport => ({
          value: airport.id,
          label: `${airport.name} (${airport.code}) - ${airport.city}`,
          code: airport.code
        }));
        setAirports(airportOptions);
      }
    } catch (error) {
      console.error('Error loading airports:', error);
    }
  };

  const loadAirlines = async () => {
    try {
      const response = await fetch('/api/flights/airlines/list');
      const data = await response.json();
      if (data.success) {
        const airlineOptions = data.data.map(airline => ({
          value: airline.id,
          label: `${airline.name} (${airline.code})`,
          code: airline.code
        }));
        setAirlines(airlineOptions);
      }
    } catch (error) {
      console.error('Error loading airlines:', error);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    
    if (!searchData.fromAirport || !searchData.toAirport) {
      alert('يرجى اختيار مطار المغادرة والوصول');
      return;
    }

    if (searchData.fromAirport.value === searchData.toAirport.value) {
      alert('مطار المغادرة والوصول لا يمكن أن يكونا نفس المطار');
      return;
    }

    const searchParams = {
      from_airport: searchData.fromAirport.value,
      to_airport: searchData.toAirport.value,
      departure_date: searchData.departureDate.toISOString().split('T')[0],
      passengers: searchData.passengers,
      seat_class: searchData.seatClass
    };

    onSearch(searchParams);
  };

  const swapAirports = () => {
    setSearchData(prev => ({
      ...prev,
      fromAirport: prev.toAirport,
      toAirport: prev.fromAirport
    }));
  };

  return (
    <Card className="mb-4">
      <Card.Header>
        <h5 className="mb-0">
          <i className="fas fa-plane me-2"></i>
          البحث عن الرحلات الجوية
        </h5>
      </Card.Header>
      <Card.Body>
        <Form onSubmit={handleSearch}>
          <Row className="mb-3">
            <Col md={5}>
              <Form.Group>
                <Form.Label>من</Form.Label>
                <Select
                  value={searchData.fromAirport}
                  onChange={(selected) => setSearchData(prev => ({ ...prev, fromAirport: selected }))}
                  options={airports}
                  placeholder="اختر مطار المغادرة"
                  isSearchable
                  className="react-select-container"
                  classNamePrefix="react-select"
                />
              </Form.Group>
            </Col>
            
            <Col md={2} className="d-flex align-items-end justify-content-center">
              <Button 
                variant="outline-primary" 
                onClick={swapAirports}
                className="mb-3"
                title="تبديل المطارات"
              >
                <i className="fas fa-exchange-alt"></i>
              </Button>
            </Col>
            
            <Col md={5}>
              <Form.Group>
                <Form.Label>إلى</Form.Label>
                <Select
                  value={searchData.toAirport}
                  onChange={(selected) => setSearchData(prev => ({ ...prev, toAirport: selected }))}
                  options={airports}
                  placeholder="اختر مطار الوصول"
                  isSearchable
                  className="react-select-container"
                  classNamePrefix="react-select"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={3}>
              <Form.Group>
                <Form.Label>تاريخ المغادرة</Form.Label>
                <DatePicker
                  selected={searchData.departureDate}
                  onChange={(date) => setSearchData(prev => ({ ...prev, departureDate: date }))}
                  minDate={new Date()}
                  dateFormat="dd/MM/yyyy"
                  className="form-control"
                  placeholderText="اختر التاريخ"
                />
              </Form.Group>
            </Col>
            
            <Col md={3}>
              <Form.Group>
                <Form.Label>عدد المسافرين</Form.Label>
                <Form.Select
                  value={searchData.passengers}
                  onChange={(e) => setSearchData(prev => ({ ...prev, passengers: parseInt(e.target.value) }))}
                >
                  {[1,2,3,4,5,6,7,8,9].map(num => (
                    <option key={num} value={num}>{num} مسافر{num > 1 ? 'ين' : ''}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            
            <Col md={3}>
              <Form.Group>
                <Form.Label>درجة السفر</Form.Label>
                <Form.Select
                  value={searchData.seatClass}
                  onChange={(e) => setSearchData(prev => ({ ...prev, seatClass: e.target.value }))}
                >
                  <option value="economy">اقتصادية</option>
                  <option value="business">رجال أعمال</option>
                  <option value="first">درجة أولى</option>
                </Form.Select>
              </Form.Group>
            </Col>
            
            <Col md={3} className="d-flex align-items-end">
              <Button 
                type="submit" 
                variant="primary" 
                className="w-100"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    جاري البحث...
                  </>
                ) : (
                  <>
                    <i className="fas fa-search me-2"></i>
                    بحث
                  </>
                )}
              </Button>
            </Col>
          </Row>
        </Form>
      </Card.Body>
    </Card>
  );
};

export default FlightSearch;
