import 'package:flutter_test/flutter_test.dart';
import 'package:travel_offers/data/services/cache_service.dart';
import 'package:travel_offers/data/services/error_service.dart';
import 'package:travel_offers/data/services/notification_service.dart';
import 'package:travel_offers/data/services/flight_service.dart';
import 'package:travel_offers/data/services/event_service.dart';
import 'package:travel_offers/data/services/kuwait_weather_service.dart';
import 'package:travel_offers/data/services/kuwait_holidays_service.dart';
import 'package:travel_offers/data/services/kuwait_payment_service.dart';
import 'package:travel_offers/data/model/kuwait/kuwait_area_model.dart';

void main() {
  group('Travel Offers App Integration Tests', () {
    setUpAll(() async {
      // Initialize services
      await CacheService.init();
      await NotificationService.init();
    });

    group('Cache Service Tests', () {
      test('should store and retrieve cache data', () async {
        const testKey = 'test_cache';
        const testData = {'message': 'Hello World'};
        
        await CacheService.setCache(testKey, testData);
        final retrievedData = CacheService.getCache<Map<String, dynamic>>(testKey);
        
        expect(retrievedData, equals(testData));
      });

      test('should handle cache expiration', () async {
        const testKey = 'test_expiry';
        const testData = {'message': 'Expires soon'};
        
        await CacheService.setCache(
          testKey, 
          testData, 
          duration: Duration(milliseconds: 100),
        );
        
        // Wait for cache to expire
        await Future.delayed(Duration(milliseconds: 150));
        
        final retrievedData = CacheService.getCache<Map<String, dynamic>>(testKey);
        expect(retrievedData, isNull);
      });

      test('should manage user preferences', () async {
        await CacheService.setUserPreference('theme', 'dark');
        await CacheService.setUserPreference('language', 'ar');
        
        expect(CacheService.getUserPreference<String>('theme'), equals('dark'));
        expect(CacheService.getUserPreference<String>('language'), equals('ar'));
      });

      test('should manage search history', () async {
        await CacheService.addSearchHistory('Dubai', 'flight');
        await CacheService.addSearchHistory('Football', 'event');
        
        final history = CacheService.getSearchHistory();
        expect(history.length, equals(2));
        expect(history[0]['query'], equals('Football'));
        expect(history[1]['query'], equals('Dubai'));
      });

      test('should manage favorites', () async {
        const flightData = {'id': 'FL123', 'destination': 'Dubai'};
        
        await CacheService.addFavorite('FL123', 'flight', flightData);
        expect(CacheService.isFavorite('FL123', 'flight'), isTrue);
        
        await CacheService.removeFavorite('FL123', 'flight');
        expect(CacheService.isFavorite('FL123', 'flight'), isFalse);
      });
    });

    group('Error Service Tests', () {
      test('should create and log network error', () async {
        final error = ErrorService.networkError(
          context: {'action': 'fetch_flights'},
        );
        
        await ErrorService.logError(error);
        
        expect(error.type, equals(ErrorType.network));
        expect(error.messageAr, contains('الإنترنت'));
      });

      test('should create validation error', () {
        final error = ErrorService.validationError(
          field: 'email',
          message: 'Invalid email format',
          messageAr: 'تنسيق البريد الإلكتروني غير صحيح',
        );
        
        expect(error.type, equals(ErrorType.validation));
        expect(error.details, contains('email'));
      });

      test('should provide recovery suggestions', () {
        final networkError = ErrorService.networkError();
        final suggestions = ErrorService.getRecoverySuggestions(networkError);
        
        expect(suggestions.isNotEmpty, isTrue);
        expect(suggestions.first, contains('اتصال'));
      });

      test('should get error statistics', () async {
        // Log some test errors
        await ErrorService.logError(ErrorService.networkError());
        await ErrorService.logError(ErrorService.serverError());
        
        final stats = ErrorService.getErrorStatistics();
        expect(stats['total'], greaterThan(0));
        expect(stats['byType'], isA<Map>());
      });
    });

    group('Flight Service Tests', () {
      test('should get flights list', () async {
        final flights = await FlightService.getFlights();
        expect(flights, isA<List>());
      });

      test('should get flight details', () async {
        final flight = await FlightService.getFlightDetails(1);
        expect(flight, isNotNull);
      });

      test('should handle booking', () async {
        final booking = await FlightService.bookFlight(
          flightId: 1,
          passengers: [],
          token: 'test_token',
        );

        expect(booking, isNotNull);
        expect(booking.bookingReference, isNotNull);
      });
    });

    group('Event Service Tests', () {
      test('should get events list', () async {
        final events = await EventService.getEvents();
        expect(events, isA<List>());
      });

      test('should get event details', () async {
        final event = await EventService.getEventDetails(1);
        expect(event, isNotNull);
      });

      test('should handle ticket booking', () async {
        final booking = await EventService.bookTickets(
          eventId: 1,
          ticketCategoryId: 1,
          quantity: 2,
          token: 'test_token',
        );

        expect(booking, isNotNull);
        expect(booking.bookingReference, isNotNull);
      });
    });

    group('Kuwait Services Tests', () {
      test('should get Kuwait areas', () {
        final governorates = KuwaitAreasService.getKuwaitGovernorates();
        
        expect(governorates.length, equals(6));
        expect(governorates.first.nameAr, equals('العاصمة'));
        
        final popularAreas = KuwaitAreasService.getPopularAreas();
        expect(popularAreas.isNotEmpty, isTrue);
      });

      test('should search areas', () {
        final results = KuwaitAreasService.searchAreas('سالمية');
        expect(results.isNotEmpty, isTrue);
        expect(results.first.nameAr, contains('السالمية'));
      });

      test('should get weather data', () async {
        final weather = await KuwaitWeatherService.getCurrentWeather();
        
        expect(weather, isNotNull);
        expect(weather.temperature, isA<double>());
        expect(weather.conditionAr, isNotEmpty);
      });

      test('should get prayer times', () async {
        final prayerTimes = await KuwaitWeatherService.getPrayerTimes();
        
        expect(prayerTimes, isNotNull);
        expect(prayerTimes.fajrTime, isNotEmpty);
        expect(prayerTimes.maghribTime, isNotEmpty);
      });

      test('should get Kuwait holidays', () {
        final holidays = KuwaitHolidaysService.getKuwaitHolidays(2024);
        
        expect(holidays.isNotEmpty, isTrue);
        
        final nationalDay = holidays.firstWhere(
          (h) => h.nameAr.contains('الوطني'),
        );
        expect(nationalDay.date.month, equals(2));
        expect(nationalDay.date.day, equals(25));
      });

      test('should get upcoming holidays', () {
        final upcoming = KuwaitHolidaysService.getUpcomingHolidays(limit: 3);
        expect(upcoming.length, lessThanOrEqualTo(3));
      });
    });

    group('Payment Service Tests', () {
      test('should get available payment methods', () {
        final methods = KuwaitPaymentService.getAvailablePaymentMethods();
        
        expect(methods.isNotEmpty, isTrue);
        
        final knet = methods.firstWhere((m) => m.type == PaymentMethod.knet);
        expect(knet.nameAr, equals('كي نت'));
        expect(knet.isAvailable, isTrue);
      });

      test('should calculate processing fees', () {
        final knetFee = KuwaitPaymentService.calculateProcessingFee(
          PaymentMethod.knet, 
          100.0,
        );
        expect(knetFee, equals(0.0));
        
        final visaFee = KuwaitPaymentService.calculateProcessingFee(
          PaymentMethod.visa, 
          100.0,
        );
        expect(visaFee, equals(0.5));
      });

      test('should get Kuwait banks', () {
        final banks = KuwaitPaymentService.getKuwaitBanks();
        
        expect(banks.isNotEmpty, isTrue);
        expect(banks.any((bank) => bank.contains('NBK')), isTrue);
        expect(banks.any((bank) => bank.contains('KFH')), isTrue);
      });

      test('should initiate payment', () async {
        final transaction = await KuwaitPaymentService.initiatePayment(
          amount: 50.0,
          method: PaymentMethod.knet,
          currency: 'KWD',
          metadata: {'booking_id': 'BK123'},
        );
        
        expect(transaction, isNotNull);
        expect(transaction.amount, equals(50.0));
        expect(transaction.currency, equals('KWD'));
        expect(transaction.method, equals(PaymentMethod.knet));
      });

      test('should provide payment tips', () {
        final knetTips = KuwaitPaymentService.getPaymentTips(PaymentMethod.knet);
        expect(knetTips.isNotEmpty, isTrue);
        expect(knetTips.any((tip) => tip.contains('PIN')), isTrue);
        
        final cardTips = KuwaitPaymentService.getPaymentTips(PaymentMethod.visa);
        expect(cardTips.isNotEmpty, isTrue);
        expect(cardTips.any((tip) => tip.contains('بطاقة')), isTrue);
      });
    });

    group('Notification Service Tests', () {
      test('should schedule flight reminder', () async {
        await NotificationService.scheduleFlightReminder(
          flightNumber: 'KU123',
          departureTime: DateTime.now().add(Duration(hours: 3)),
          from: 'Kuwait',
          to: 'Dubai',
        );
        
        final notifications = NotificationService.getStoredNotifications();
        expect(notifications.any((n) => n.id.contains('KU123')), isTrue);
      });

      test('should show payment confirmation', () async {
        await NotificationService.showPaymentConfirmation(
          bookingReference: 'BK123',
          amount: 150.0,
          currency: 'KWD',
        );
        
        final notifications = NotificationService.getStoredNotifications();
        expect(notifications.any((n) => n.id.contains('BK123')), isTrue);
      });

      test('should manage notification read status', () async {
        await NotificationService.showPaymentConfirmation(
          bookingReference: 'BK456',
          amount: 75.0,
          currency: 'KWD',
        );
        
        final unreadCount = NotificationService.getUnreadCount();
        expect(unreadCount, greaterThan(0));
        
        await NotificationService.markAllAsRead();
        final newUnreadCount = NotificationService.getUnreadCount();
        expect(newUnreadCount, equals(0));
      });
    });

    group('App Performance Tests', () {
      test('should handle large data sets efficiently', () async {
        final stopwatch = Stopwatch()..start();

        // Test with flight data
        final flights = await FlightService.getFlights();

        stopwatch.stop();

        // Should complete within reasonable time (5 seconds)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
        expect(flights, isA<List>());
      });

      test('should handle concurrent operations', () async {
        final futures = <Future>[];

        // Simulate concurrent API calls
        futures.add(FlightService.getFlights());
        futures.add(EventService.getEvents());
        futures.add(KuwaitWeatherService.getCurrentWeather());

        final results = await Future.wait(futures);

        expect(results.length, equals(3));
        expect(results.every((result) => result != null), isTrue);
      });

      test('should manage memory efficiently', () {
        // Test cache size management
        final initialSize = CacheService.getCacheSize();
        
        // Add some cache data
        for (int i = 0; i < 100; i++) {
          CacheService.setCache('test_$i', {'data': 'test_data_$i'});
        }
        
        final afterSize = CacheService.getCacheSize();
        expect(afterSize, greaterThan(initialSize));
        
        // Clear cache
        CacheService.clearAllCache();
        
        final finalSize = CacheService.getCacheSize();
        expect(finalSize, lessThan(afterSize));
      });
    });

    group('Error Handling Tests', () {
      test('should handle network errors gracefully', () async {
        try {
          // This should trigger a network error in test environment
          await FlightService.getFlightDetails(-1);
        } catch (e) {
          expect(e, isA<Exception>());
        }
      });

      test('should validate input data', () {
        expect(
          () => ErrorService.validationError(field: ''),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should recover from errors', () async {
        final error = ErrorService.networkError();
        final suggestions = ErrorService.getRecoverySuggestions(error);
        
        expect(suggestions.isNotEmpty, isTrue);
        expect(suggestions.every((s) => s.isNotEmpty), isTrue);
      });
    });

    tearDownAll(() async {
      // Cleanup
      await CacheService.clearAllCache();
      await NotificationService.deleteAllNotifications();
      await ErrorService.clearStoredErrors();
    });
  });
}
