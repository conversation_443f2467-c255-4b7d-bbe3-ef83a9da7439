<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class TicketBooking extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'event_id',
        'ticket_category_id',
        'quantity',
        'total_price',
        'booking_reference',
        'qr_code',
        'ticket_details',
        'status',
        'payment_status',
        'booking_date',
        'used_at',
        'special_requests'
    ];

    protected $casts = [
        'total_price' => 'decimal:2',
        'ticket_details' => 'array',
        'booking_date' => 'datetime',
        'used_at' => 'datetime'
    ];

    protected $appends = ['status_text', 'payment_status_text'];

    /**
     * Get status text
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'pending' => 'في الانتظار',
            'confirmed' => 'مؤكد',
            'cancelled' => 'ملغي',
            'used' => 'مستخدم',
            default => 'غير محدد'
        };
    }

    /**
     * Get payment status text
     */
    public function getPaymentStatusTextAttribute()
    {
        return match($this->payment_status) {
            'pending' => 'في الانتظار',
            'paid' => 'مدفوع',
            'failed' => 'فشل',
            'refunded' => 'مسترد',
            default => 'غير محدد'
        };
    }

    /**
     * Generate unique booking reference
     */
    public static function generateBookingReference()
    {
        do {
            $reference = 'TK' . strtoupper(substr(uniqid(), -8));
        } while (self::where('booking_reference', $reference)->exists());

        return $reference;
    }

    /**
     * Generate QR code
     */
    public function generateQrCode()
    {
        $qrData = [
            'booking_reference' => $this->booking_reference,
            'event_id' => $this->event_id,
            'user_id' => $this->user_id,
            'quantity' => $this->quantity,
            'verification_url' => route('ticket.verify', $this->booking_reference)
        ];

        $qrCodePath = 'qr_codes/tickets/' . $this->booking_reference . '.png';
        
        QrCode::format('png')
              ->size(300)
              ->generate(json_encode($qrData), storage_path('app/public/' . $qrCodePath));

        $this->update(['qr_code' => $qrCodePath]);
        
        return $qrCodePath;
    }

    /**
     * Get user
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get event
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get ticket category
     */
    public function ticketCategory()
    {
        return $this->belongsTo(TicketCategory::class);
    }

    /**
     * Get individual tickets
     */
    public function individualTickets()
    {
        return $this->hasMany(IndividualTicket::class);
    }

    /**
     * Get payment transactions
     */
    public function paymentTransactions()
    {
        return $this->hasMany(PaymentTransaction::class, 'booking_id');
    }

    /**
     * Check if booking is confirmed
     */
    public function isConfirmed()
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if booking is paid
     */
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }

    /**
     * Check if tickets are used
     */
    public function isUsed()
    {
        return $this->status === 'used';
    }

    /**
     * Confirm booking
     */
    public function confirm()
    {
        $this->update(['status' => 'confirmed']);
        
        // Generate individual tickets
        $this->generateIndividualTickets();
    }

    /**
     * Cancel booking
     */
    public function cancel()
    {
        $this->update(['status' => 'cancelled']);
        
        // Release tickets back to category
        $this->ticketCategory->releaseTickets($this->quantity);
    }

    /**
     * Mark as paid
     */
    public function markAsPaid()
    {
        $this->update(['payment_status' => 'paid']);
        
        if ($this->status === 'pending') {
            $this->confirm();
        }
    }

    /**
     * Mark as used
     */
    public function markAsUsed()
    {
        $this->update([
            'status' => 'used',
            'used_at' => now()
        ]);
    }

    /**
     * Generate individual tickets
     */
    public function generateIndividualTickets()
    {
        for ($i = 1; $i <= $this->quantity; $i++) {
            IndividualTicket::create([
                'ticket_booking_id' => $this->id,
                'ticket_number' => $this->booking_reference . '-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'qr_code' => $this->generateIndividualQrCode($i),
                'seat_number' => $this->generateSeatNumber($i),
                'status' => 'active'
            ]);
        }
    }

    /**
     * Generate individual QR code
     */
    private function generateIndividualQrCode($ticketNumber)
    {
        return 'QR-' . $this->booking_reference . '-' . $ticketNumber;
    }

    /**
     * Generate seat number
     */
    private function generateSeatNumber($ticketNumber)
    {
        // This can be customized based on venue seating arrangement
        return $this->ticketCategory->section . '-' . $ticketNumber;
    }

    /**
     * Scope for confirmed bookings
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope for paid bookings
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    /**
     * Scope by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope by event
     */
    public function scopeByEvent($query, $eventId)
    {
        return $query->where('event_id', $eventId);
    }
}
