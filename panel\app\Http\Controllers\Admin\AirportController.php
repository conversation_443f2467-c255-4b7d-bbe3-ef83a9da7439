<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Airport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AirportController extends Controller
{
    public function index(Request $request)
    {
        $query = Airport::query();

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        if ($request->filled('country')) {
            $query->where('country', $request->country);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $airports = $query->orderBy('name')->paginate(20);

        return view('admin.airports.index', compact('airports'));
    }

    public function create()
    {
        return view('admin.airports.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:airports,code',
            'city' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'address' => 'nullable|string',
            'timezone' => 'nullable|string|max:50',
            'map_lat' => 'nullable|numeric',
            'map_lng' => 'nullable|numeric',
            'status' => 'required|in:publish,draft'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        Airport::create(array_merge($request->all(), [
            'create_user' => auth()->id()
        ]));

        return redirect()->route('admin.airports.index')
                        ->with('success', 'تم إنشاء المطار بنجاح');
    }

    public function show(Airport $airport)
    {
        $airport->load(['departureFlights', 'arrivalFlights']);
        return view('admin.airports.show', compact('airport'));
    }

    public function edit(Airport $airport)
    {
        return view('admin.airports.edit', compact('airport'));
    }

    public function update(Request $request, Airport $airport)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:airports,code,' . $airport->id,
            'city' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'address' => 'nullable|string',
            'timezone' => 'nullable|string|max:50',
            'map_lat' => 'nullable|numeric',
            'map_lng' => 'nullable|numeric',
            'status' => 'required|in:publish,draft'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $airport->update(array_merge($request->all(), [
            'update_user' => auth()->id()
        ]));

        return redirect()->route('admin.airports.index')
                        ->with('success', 'تم تحديث المطار بنجاح');
    }

    public function destroy(Airport $airport)
    {
        if ($airport->departureFlights()->exists() || $airport->arrivalFlights()->exists()) {
            return redirect()->back()
                           ->with('error', 'لا يمكن حذف المطار لوجود رحلات مرتبطة به');
        }

        $airport->delete();

        return redirect()->route('admin.airports.index')
                        ->with('success', 'تم حذف المطار بنجاح');
    }
}
