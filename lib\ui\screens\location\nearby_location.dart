import 'package:flutter/material.dart';
import 'package:travel_offers/ui/theme/theme.dart';
import 'package:travel_offers/utils/extensions/extensions.dart';

class NearbyLocationScreen extends StatefulWidget {
  const NearbyLocationScreen({Key? key}) : super(key: key);

  @override
  State<NearbyLocationScreen> createState() => NearbyLocationScreenState();
}

class NearbyLocationScreenState extends State<NearbyLocationScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('المواقع القريبة'),
        backgroundColor: context.color.territoryColor,
        elevation: 0,
        iconTheme: IconThemeData(color: context.color.textColorDark),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 100,
              color: Colors.grey[400],
            ),
            SizedBox(height: 20),
            Text(
              'خدمة المواقع القريبة غير متاحة حالياً',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 30),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.color.territoryColor,
                padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
              child: Text(
                'العودة',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
