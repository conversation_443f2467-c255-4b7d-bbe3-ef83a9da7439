class KuwaitGovernorateModel {
  final int id;
  final String nameEn;
  final String nameAr;
  final String code;
  final List<KuwaitAreaModel> areas;

  KuwaitGovernorateModel({
    required this.id,
    required this.nameEn,
    required this.nameAr,
    required this.code,
    required this.areas,
  });

  factory KuwaitGovernorateModel.fromJson(Map<String, dynamic> json) {
    return KuwaitGovernorateModel(
      id: json['id'] ?? 0,
      nameEn: json['name_en'] ?? '',
      nameAr: json['name_ar'] ?? '',
      code: json['code'] ?? '',
      areas: (json['areas'] as List<dynamic>?)
          ?.map((area) => KuwaitAreaModel.fromJson(area))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name_en': nameEn,
      'name_ar': nameAr,
      'code': code,
      'areas': areas.map((area) => area.toJson()).toList(),
    };
  }

  String get displayName {
    return nameAr.isNotEmpty ? nameAr : nameEn;
  }
}

class KuwaitAreaModel {
  final int id;
  final String nameEn;
  final String nameAr;
  final String code;
  final int governorateId;
  final double? latitude;
  final double? longitude;
  final List<String> landmarks;
  final bool isPopular;

  KuwaitAreaModel({
    required this.id,
    required this.nameEn,
    required this.nameAr,
    required this.code,
    required this.governorateId,
    this.latitude,
    this.longitude,
    this.landmarks = const [],
    this.isPopular = false,
  });

  factory KuwaitAreaModel.fromJson(Map<String, dynamic> json) {
    return KuwaitAreaModel(
      id: json['id'] ?? 0,
      nameEn: json['name_en'] ?? '',
      nameAr: json['name_ar'] ?? '',
      code: json['code'] ?? '',
      governorateId: json['governorate_id'] ?? 0,
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      landmarks: (json['landmarks'] as List<dynamic>?)
          ?.map((landmark) => landmark.toString())
          .toList() ?? [],
      isPopular: json['is_popular'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name_en': nameEn,
      'name_ar': nameAr,
      'code': code,
      'governorate_id': governorateId,
      'latitude': latitude,
      'longitude': longitude,
      'landmarks': landmarks,
      'is_popular': isPopular,
    };
  }

  String get displayName {
    return nameAr.isNotEmpty ? nameAr : nameEn;
  }
}

class KuwaitAreasService {
  static List<KuwaitGovernorateModel> getKuwaitGovernorates() {
    return [
      // العاصمة
      KuwaitGovernorateModel(
        id: 1,
        nameEn: 'Capital',
        nameAr: 'العاصمة',
        code: 'CAP',
        areas: [
          KuwaitAreaModel(
            id: 1,
            nameEn: 'Kuwait City',
            nameAr: 'مدينة الكويت',
            code: 'KWC',
            governorateId: 1,
            latitude: 29.3759,
            longitude: 47.9774,
            landmarks: ['Kuwait Towers', 'Grand Mosque', 'Seif Palace'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 2,
            nameEn: 'Sharq',
            nameAr: 'شرق',
            code: 'SHQ',
            governorateId: 1,
            latitude: 29.3697,
            longitude: 47.9735,
            landmarks: ['Sharq Mall', 'Kuwait Stock Exchange'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 3,
            nameEn: 'Mirqab',
            nameAr: 'المرقاب',
            code: 'MRQ',
            governorateId: 1,
            latitude: 29.3728,
            longitude: 47.9892,
            landmarks: ['Liberation Tower', 'Al Hamra Tower'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 4,
            nameEn: 'Dasman',
            nameAr: 'دسمان',
            code: 'DSM',
            governorateId: 1,
            latitude: 29.3833,
            longitude: 47.9833,
            landmarks: ['Dasman Palace', 'National Assembly'],
          ),
          KuwaitAreaModel(
            id: 5,
            nameEn: 'Qibla',
            nameAr: 'القبلة',
            code: 'QBL',
            governorateId: 1,
            latitude: 29.3667,
            longitude: 47.9667,
            landmarks: ['Souq Al-Mubarakiya', 'Grand Mosque'],
          ),
          KuwaitAreaModel(
            id: 6,
            nameEn: 'Adailiya',
            nameAr: 'العديلية',
            code: 'ADL',
            governorateId: 1,
            latitude: 29.3333,
            longitude: 48.0333,
            landmarks: ['Adailiya Park', 'Scientific Center'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 7,
            nameEn: 'Khaldiya',
            nameAr: 'الخالدية',
            code: 'KHL',
            governorateId: 1,
            latitude: 29.3167,
            longitude: 48.0167,
            landmarks: ['Khaldiya Park', 'Marina Mall'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 8,
            nameEn: 'Qadisiya',
            nameAr: 'القادسية',
            code: 'QDS',
            governorateId: 1,
            latitude: 29.3000,
            longitude: 48.0500,
            landmarks: ['Qadisiya Sports Club', 'Qadisiya Park'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 9,
            nameEn: 'Mansouriya',
            nameAr: 'المنصورية',
            code: 'MNS',
            governorateId: 1,
            latitude: 29.2833,
            longitude: 48.0333,
            landmarks: ['Mansouriya Park'],
          ),
          KuwaitAreaModel(
            id: 10,
            nameEn: 'Nuzha',
            nameAr: 'النزهة',
            code: 'NZH',
            governorateId: 1,
            latitude: 29.2667,
            longitude: 48.0167,
            landmarks: ['Nuzha Park'],
          ),
        ],
      ),
      
      // حولي
      KuwaitGovernorateModel(
        id: 2,
        nameEn: 'Hawalli',
        nameAr: 'حولي',
        code: 'HAW',
        areas: [
          KuwaitAreaModel(
            id: 11,
            nameEn: 'Hawalli',
            nameAr: 'حولي',
            code: 'HAW',
            governorateId: 2,
            latitude: 29.3333,
            longitude: 48.0167,
            landmarks: ['Hawalli Park', 'Hawalli Governorate'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 12,
            nameEn: 'Salmiya',
            nameAr: 'السالمية',
            code: 'SLM',
            governorateId: 2,
            latitude: 29.3333,
            longitude: 48.0667,
            landmarks: ['Marina Mall', 'Scientific Center', 'Salmiya Beach'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 13,
            nameEn: 'Rumaithiya',
            nameAr: 'الرميثية',
            code: 'RMT',
            governorateId: 2,
            latitude: 29.3167,
            longitude: 48.0833,
            landmarks: ['Rumaithiya Park', 'Sultan Center'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 14,
            nameEn: 'Jabriya',
            nameAr: 'الجابرية',
            code: 'JBR',
            governorateId: 2,
            latitude: 29.3167,
            longitude: 48.1000,
            landmarks: ['Kuwait University', 'Jabriya Park'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 15,
            nameEn: 'Surra',
            nameAr: 'الصرة',
            code: 'SRR',
            governorateId: 2,
            latitude: 29.3000,
            longitude: 48.0833,
            landmarks: ['Surra Park'],
          ),
          KuwaitAreaModel(
            id: 16,
            nameEn: 'Shaab',
            nameAr: 'الشعب',
            code: 'SHB',
            governorateId: 2,
            latitude: 29.3500,
            longitude: 48.0333,
            landmarks: ['Shaab Park', 'Kuwait Club'],
          ),
          KuwaitAreaModel(
            id: 17,
            nameEn: 'Mishref',
            nameAr: 'مشرف',
            code: 'MSH',
            governorateId: 2,
            latitude: 29.2833,
            longitude: 48.1167,
            landmarks: ['Mishref Park', 'Mishref Co-op'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 18,
            nameEn: 'Bayan',
            nameAr: 'بيان',
            code: 'BYN',
            governorateId: 2,
            latitude: 29.3000,
            longitude: 48.1000,
            landmarks: ['Bayan Palace', 'Bayan Park'],
          ),
          KuwaitAreaModel(
            id: 19,
            nameEn: 'Salwa',
            nameAr: 'سلوى',
            code: 'SLW',
            governorateId: 2,
            latitude: 29.2833,
            longitude: 48.0833,
            landmarks: ['Salwa Park', 'Salwa Co-op'],
            isPopular: true,
          ),
        ],
      ),
      
      // الفروانية
      KuwaitGovernorateModel(
        id: 3,
        nameEn: 'Farwaniya',
        nameAr: 'الفروانية',
        code: 'FAR',
        areas: [
          KuwaitAreaModel(
            id: 20,
            nameEn: 'Farwaniya',
            nameAr: 'الفروانية',
            code: 'FAR',
            governorateId: 3,
            latitude: 29.2833,
            longitude: 47.9333,
            landmarks: ['Farwaniya Hospital', 'Farwaniya Park'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 21,
            nameEn: 'Jleeb Al-Shuyoukh',
            nameAr: 'جليب الشيوخ',
            code: 'JLB',
            governorateId: 3,
            latitude: 29.2667,
            longitude: 47.9167,
            landmarks: ['Jleeb Market'],
          ),
          KuwaitAreaModel(
            id: 22,
            nameEn: 'Rabiya',
            nameAr: 'الرابية',
            code: 'RBY',
            governorateId: 3,
            latitude: 29.2833,
            longitude: 47.9167,
            landmarks: ['Rabiya Park'],
          ),
        ],
      ),
      
      // الأحمدي
      KuwaitGovernorateModel(
        id: 4,
        nameEn: 'Ahmadi',
        nameAr: 'الأحمدي',
        code: 'AHM',
        areas: [
          KuwaitAreaModel(
            id: 23,
            nameEn: 'Ahmadi',
            nameAr: 'الأحمدي',
            code: 'AHM',
            governorateId: 4,
            latitude: 29.0833,
            longitude: 48.0833,
            landmarks: ['Ahmadi Park', 'Oil Museum'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 24,
            nameEn: 'Fahaheel',
            nameAr: 'الفحيحيل',
            code: 'FHH',
            governorateId: 4,
            latitude: 29.0833,
            longitude: 48.1333,
            landmarks: ['Al Kout Mall', 'Fahaheel Beach'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 25,
            nameEn: 'Mangaf',
            nameAr: 'المنقف',
            code: 'MNG',
            governorateId: 4,
            latitude: 29.1000,
            longitude: 48.1167,
            landmarks: ['Mangaf Beach', 'Mangaf Park'],
            isPopular: true,
          ),
        ],
      ),
      
      // مبارك الكبير
      KuwaitGovernorateModel(
        id: 5,
        nameEn: 'Mubarak Al-Kabeer',
        nameAr: 'مبارك الكبير',
        code: 'MBK',
        areas: [
          KuwaitAreaModel(
            id: 26,
            nameEn: 'Qurain',
            nameAr: 'القرين',
            code: 'QRN',
            governorateId: 5,
            latitude: 29.2167,
            longitude: 48.1333,
            landmarks: ['Qurain Cultural District'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 27,
            nameEn: 'Adan',
            nameAr: 'العدان',
            code: 'ADN',
            governorateId: 5,
            latitude: 29.2000,
            longitude: 48.1167,
            landmarks: ['Adan Hospital'],
          ),
        ],
      ),
      
      // الجهراء
      KuwaitGovernorateModel(
        id: 6,
        nameEn: 'Jahra',
        nameAr: 'الجهراء',
        code: 'JHR',
        areas: [
          KuwaitAreaModel(
            id: 28,
            nameEn: 'Jahra',
            nameAr: 'الجهراء',
            code: 'JHR',
            governorateId: 6,
            latitude: 29.3333,
            longitude: 47.6667,
            landmarks: ['Jahra Fort', 'Jahra Park'],
            isPopular: true,
          ),
          KuwaitAreaModel(
            id: 29,
            nameEn: 'Sulaibiya',
            nameAr: 'الصليبية',
            code: 'SLB',
            governorateId: 6,
            latitude: 29.3167,
            longitude: 47.7000,
            landmarks: ['Sulaibiya Park'],
          ),
        ],
      ),
    ];
  }

  static List<KuwaitAreaModel> getPopularAreas() {
    return getKuwaitGovernorates()
        .expand((governorate) => governorate.areas)
        .where((area) => area.isPopular)
        .toList();
  }

  static KuwaitAreaModel? getAreaById(int id) {
    try {
      return getKuwaitGovernorates()
          .expand((governorate) => governorate.areas)
          .firstWhere((area) => area.id == id);
    } catch (e) {
      return null;
    }
  }

  static KuwaitGovernorateModel? getGovernorateById(int id) {
    try {
      return getKuwaitGovernorates()
          .firstWhere((governorate) => governorate.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<KuwaitAreaModel> getAreasByGovernorate(int governorateId) {
    final governorate = getGovernorateById(governorateId);
    return governorate?.areas ?? [];
  }

  static List<KuwaitAreaModel> searchAreas(String query) {
    final lowercaseQuery = query.toLowerCase();
    return getKuwaitGovernorates()
        .expand((governorate) => governorate.areas)
        .where((area) =>
            area.nameEn.toLowerCase().contains(lowercaseQuery) ||
            area.nameAr.contains(query) ||
            area.landmarks.any((landmark) => 
                landmark.toLowerCase().contains(lowercaseQuery)))
        .toList();
  }
}
