import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:travel_offers/data/model/destination_model.dart';
import 'package:travel_offers/ui/theme/theme.dart';
import 'package:travel_offers/utils/app_icon.dart';
import 'package:travel_offers/utils/constant.dart';
import 'package:travel_offers/utils/custom_text.dart';
import 'package:travel_offers/utils/extensions/extensions.dart';
import 'package:travel_offers/utils/helper_utils.dart';
import 'package:travel_offers/utils/ui_utils.dart';
// import 'package:intl/intl.dart';

class TravelOffersScreen extends StatefulWidget {
  final DestinationModel? selectedDestination;
  final DateTime? departureDate;
  final DateTime? returnDate;

  const TravelOffersScreen({
    Key? key,
    this.selectedDestination,
    this.departureDate,
    this.returnDate,
  }) : super(key: key);

  static Route route(RouteSettings routeSettings) {
    Map? arguments = routeSettings.arguments as Map?;
    return MaterialPageRoute(
      builder: (context) => TravelOffersScreen(
        selectedDestination: arguments?['destination'],
        departureDate: arguments?['departureDate'],
        returnDate: arguments?['returnDate'],
      ),
    );
  }

  @override
  State<TravelOffersScreen> createState() => _TravelOffersScreenState();
}

class _TravelOffersScreenState extends State<TravelOffersScreen> {
  DestinationModel? selectedDestination;
  DateTime? departureDate;
  DateTime? returnDate;
  List<TravelOffer> filteredOffers = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    selectedDestination = widget.selectedDestination;
    departureDate = widget.departureDate;
    returnDate = widget.returnDate;
    _loadTravelOffers();
  }

  void _loadTravelOffers() {
    setState(() {
      isLoading = true;
    });

    // Simulate loading travel offers
    Future.delayed(Duration(seconds: 2), () {
      setState(() {
        filteredOffers = _generateSampleOffers();
        isLoading = false;
      });
    });
  }

  List<TravelOffer> _generateSampleOffers() {
    // Sample travel offers based on destination and dates
    if (selectedDestination == null) return [];

    return [
      TravelOffer(
        id: '1',
        title: 'رحلة إلى ${selectedDestination!.name}',
        description: 'رحلة مميزة تشمل الإقامة والطيران',
        price: 2500.0,
        originalPrice: 3000.0,
        duration: '5 أيام / 4 ليالي',
        imageUrl: 'https://via.placeholder.com/300x200',
        includes: ['طيران ذهاب وإياب', 'إقامة 4 نجوم', 'وجبة الإفطار'],
        excludes: ['التأشيرة', 'الوجبات الأخرى'],
        validFrom: departureDate ?? DateTime.now(),
        validTo: returnDate ?? DateTime.now().add(Duration(days: 30)),
      ),
      TravelOffer(
        id: '2',
        title: 'عرض خاص - ${selectedDestination!.name}',
        description: 'عرض محدود لفترة قصيرة',
        price: 1800.0,
        originalPrice: 2200.0,
        duration: '3 أيام / 2 ليلة',
        imageUrl: 'https://via.placeholder.com/300x200',
        includes: ['طيران', 'إقامة 3 نجوم'],
        excludes: ['الوجبات', 'المواصلات الداخلية'],
        validFrom: departureDate ?? DateTime.now(),
        validTo: returnDate ?? DateTime.now().add(Duration(days: 15)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.color.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.color.territoryColor,
        title: CustomText(
          "travelOffers".translate(context),
          color: context.color.buttonColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        leading: Material(
          clipBehavior: Clip.antiAlias,
          color: Colors.transparent,
          type: MaterialType.circle,
          child: InkWell(
            onTap: () => Navigator.pop(context),
            child: Padding(
              padding: const EdgeInsetsDirectional.only(
                start: 18.0,
              ),
              child: UiUtils.getSvg(AppIcons.arrowLeft,
                  fit: BoxFit.none, color: context.color.buttonColor),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          // Filter Header
          _buildFilterHeader(),

          // Offers List
          Expanded(
            child: isLoading
                ? _buildLoadingWidget()
                : filteredOffers.isEmpty
                    ? _buildNoOffersWidget()
                    : _buildOffersList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterHeader() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.color.secondaryColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (selectedDestination != null) ...[
            Row(
              children: [
                Icon(Icons.location_on,
                     color: context.color.territoryColor, size: 20),
                SizedBox(width: 8),
                Expanded(
                  child: CustomText(
                    selectedDestination!.name ?? "وجهة غير محددة",
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: context.color.textColorDark,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
          ],

          if (departureDate != null || returnDate != null) ...[
            Row(
              children: [
                Icon(Icons.date_range,
                     color: context.color.territoryColor, size: 20),
                SizedBox(width: 8),
                Expanded(
                  child: CustomText(
                    _formatDateRange(),
                    fontSize: 14,
                    color: context.color.textColorDark.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ],

          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _showDestinationPicker,
                  icon: Icon(Icons.edit_location),
                  label: CustomText("selectDestination".translate(context)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.color.territoryColor,
                    foregroundColor: context.color.buttonColor,
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _showDatePicker,
                  icon: Icon(Icons.calendar_today),
                  label: CustomText("selectTravelDate".translate(context)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.color.territoryColor,
                    foregroundColor: context.color.buttonColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: 3,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(bottom: 16),
          child: Container(
            child: Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNoOffersWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.flight_takeoff,
            size: 80,
            color: context.color.textColorDark.withValues(alpha: 0.3),
          ),
          SizedBox(height: 16),
          CustomText(
            "noOffersFound".translate(context),
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: context.color.textColorDark,
          ),
          SizedBox(height: 8),
          CustomText(
            selectedDestination != null
                ? "لا توجد عروض متاحة لوجهة ${selectedDestination!.name}"
                : "يرجى اختيار وجهة للبحث عن العروض",
            fontSize: 14,
            color: context.color.textColorDark.withValues(alpha: 0.7),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOffersList() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: filteredOffers.length,
      itemBuilder: (context, index) {
        final offer = filteredOffers[index];
        return _buildOfferCard(offer);
      },
    );
  }

  Widget _buildOfferCard(TravelOffer offer) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: context.color.secondaryColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image
          ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
            child: Container(
              height: 150,
              width: double.infinity,
              color: context.color.territoryColor.withValues(alpha: 0.1),
              child: Icon(
                Icons.image,
                size: 50,
                color: context.color.territoryColor.withValues(alpha: 0.5),
              ),
            ),
          ),

          Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and Price
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomText(
                            offer.title,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: context.color.textColorDark,
                          ),
                          SizedBox(height: 4),
                          CustomText(
                            offer.duration,
                            fontSize: 12,
                            color: context.color.textColorDark.withValues(alpha: 0.7),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (offer.originalPrice > offer.price) ...[
                          Text(
                            "${offer.originalPrice.toStringAsFixed(0)} ${Constant.currencySymbol}",
                            style: TextStyle(
                              fontSize: 12,
                              color: context.color.textColorDark.withValues(alpha: 0.5),
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                        ],
                        CustomText(
                          "${offer.price.toStringAsFixed(0)} ${Constant.currencySymbol}",
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: context.color.territoryColor,
                        ),
                      ],
                    ),
                  ],
                ),

                SizedBox(height: 12),

                // Description
                CustomText(
                  offer.description,
                  fontSize: 14,
                  color: context.color.textColorDark.withValues(alpha: 0.8),
                ),

                SizedBox(height: 16),

                // Book Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _bookOffer(offer),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.color.territoryColor,
                      foregroundColor: context.color.buttonColor,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: CustomText(
                      "bookNow".translate(context),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateRange() {
    if (departureDate == null && returnDate == null) return '';

    String formatDate(DateTime date) {
      return "${date.day}/${date.month}/${date.year}";
    }

    if (departureDate != null && returnDate != null) {
      return "${formatDate(departureDate!)} - ${formatDate(returnDate!)}";
    } else if (departureDate != null) {
      return "من ${formatDate(departureDate!)}";
    } else {
      return "حتى ${formatDate(returnDate!)}";
    }
  }

  void _showDestinationPicker() {
    // Show destination picker dialog
    HelperUtils.showSnackBarMessage(context, "قريباً: اختيار الوجهة");
  }

  void _showDatePicker() {
    // Show date picker dialog
    HelperUtils.showSnackBarMessage(context, "قريباً: اختيار التاريخ");
  }

  void _bookOffer(TravelOffer offer) {
    HelperUtils.showSnackBarMessage(context, "تم اختيار العرض: ${offer.title}");
  }
}

// Travel Offer Model
class TravelOffer {
  final String id;
  final String title;
  final String description;
  final double price;
  final double originalPrice;
  final String duration;
  final String imageUrl;
  final List<String> includes;
  final List<String> excludes;
  final DateTime validFrom;
  final DateTime validTo;

  TravelOffer({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.originalPrice,
    required this.duration,
    required this.imageUrl,
    required this.includes,
    required this.excludes,
    required this.validFrom,
    required this.validTo,
  });
}
