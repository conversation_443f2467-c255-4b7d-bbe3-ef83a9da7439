import 'package:flutter/material.dart';
import 'package:travel_offers/data/model/item/item_model.dart';
import 'package:travel_offers/ui/theme/theme.dart';
import 'package:travel_offers/utils/extensions/extensions.dart';

class ConfirmLocationScreen extends StatefulWidget {
  final ItemModel? itemModel;
  final Function(double lat, double lng, String address)? onLocationSelected;

  const ConfirmLocationScreen({
    Key? key,
    this.itemModel,
    this.onLocationSelected,
  }) : super(key: key);

  @override
  State<ConfirmLocationScreen> createState() => _ConfirmLocationScreenState();
}

class _ConfirmLocationScreenState extends State<ConfirmLocationScreen> {
  TextEditingController addressController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController stateController = TextEditingController();
  TextEditingController countryController = TextEditingController();
  
  double? latitude;
  double? longitude;

  @override
  void initState() {
    super.initState();
    if (widget.itemModel != null) {
      latitude = widget.itemModel!.latitude;
      longitude = widget.itemModel!.longitude;
      addressController.text = widget.itemModel!.address ?? '';
      cityController.text = widget.itemModel!.city ?? '';
      stateController.text = widget.itemModel!.state ?? '';
      countryController.text = widget.itemModel!.country ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تأكيد الموقع'),
        backgroundColor: context.color.territoryColor,
        elevation: 0,
        iconTheme: IconThemeData(color: context.color.textColorDark),
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            // رسالة تنبيه
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                border: Border.all(color: Colors.orange[200]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange),
                  SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      'خدمة تحديد الموقع الجغرافي غير متاحة حالياً. يرجى إدخال العنوان يدوياً.',
                      style: TextStyle(color: Colors.orange[800]),
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 20),
            
            // حقول إدخال العنوان
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    TextFormField(
                      controller: addressController,
                      decoration: InputDecoration(
                        labelText: 'العنوان',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_on),
                      ),
                    ),
                    
                    SizedBox(height: 16),
                    
                    TextFormField(
                      controller: cityController,
                      decoration: InputDecoration(
                        labelText: 'المدينة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_city),
                      ),
                    ),
                    
                    SizedBox(height: 16),
                    
                    TextFormField(
                      controller: stateController,
                      decoration: InputDecoration(
                        labelText: 'المنطقة/الولاية',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.map),
                      ),
                    ),
                    
                    SizedBox(height: 16),
                    
                    TextFormField(
                      controller: countryController,
                      decoration: InputDecoration(
                        labelText: 'الدولة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.flag),
                      ),
                    ),
                    
                    SizedBox(height: 16),
                    
                    // عرض الإحداثيات إذا كانت متاحة
                    if (latitude != null && longitude != null)
                      Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.gps_fixed, color: Colors.grey[600]),
                            SizedBox(width: 10),
                            Text(
                              'الإحداثيات: ${latitude!.toStringAsFixed(6)}, ${longitude!.toStringAsFixed(6)}',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
            
            // أزرار التحكم
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      padding: EdgeInsets.symmetric(vertical: 15),
                    ),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
                
                SizedBox(width: 16),
                
                Expanded(
                  child: ElevatedButton(
                    onPressed: _confirmLocation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.color.territoryColor,
                      padding: EdgeInsets.symmetric(vertical: 15),
                    ),
                    child: Text(
                      'تأكيد',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _confirmLocation() {
    if (addressController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى إدخال العنوان'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    String fullAddress = [
      addressController.text,
      cityController.text,
      stateController.text,
      countryController.text,
    ].where((s) => s.isNotEmpty).join(', ');

    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(
        latitude ?? 0.0,
        longitude ?? 0.0,
        fullAddress,
      );
    }

    Navigator.pop(context, {
      'address': fullAddress,
      'city': cityController.text,
      'state': stateController.text,
      'country': countryController.text,
      'latitude': latitude,
      'longitude': longitude,
    });
  }

  @override
  void dispose() {
    addressController.dispose();
    cityController.dispose();
    stateController.dispose();
    countryController.dispose();
    super.dispose();
  }
}
