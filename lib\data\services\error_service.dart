import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:travel_offers/data/services/cache_service.dart';

enum ErrorType {
  network,
  server,
  authentication,
  validation,
  permission,
  unknown,
  cache,
  payment,
}

enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

class AppError {
  final String id;
  final ErrorType type;
  final ErrorSeverity severity;
  final String message;
  final String messageAr;
  final String? details;
  final String? stackTrace;
  final DateTime timestamp;
  final Map<String, dynamic>? context;
  final String? userId;
  final String? sessionId;

  AppError({
    required this.id,
    required this.type,
    required this.severity,
    required this.message,
    required this.messageAr,
    this.details,
    this.stackTrace,
    required this.timestamp,
    this.context,
    this.userId,
    this.sessionId,
  });

  factory AppError.fromException(
    Exception exception, {
    ErrorType? type,
    ErrorSeverity severity = ErrorSeverity.medium,
    Map<String, dynamic>? context,
  }) {
    final errorType = type ?? _determineErrorType(exception);
    final messages = _getErrorMessages(errorType, exception);
    
    return AppError(
      id: _generateErrorId(),
      type: errorType,
      severity: severity,
      message: messages['en'] ?? exception.toString(),
      messageAr: messages['ar'] ?? 'حدث خطأ غير متوقع',
      details: exception.toString(),
      stackTrace: kDebugMode ? StackTrace.current.toString() : null,
      timestamp: DateTime.now(),
      context: context,
    );
  }

  factory AppError.network({
    String? message,
    String? messageAr,
    Map<String, dynamic>? context,
  }) {
    return AppError(
      id: _generateErrorId(),
      type: ErrorType.network,
      severity: ErrorSeverity.medium,
      message: message ?? 'Network connection error',
      messageAr: messageAr ?? 'خطأ في الاتصال بالإنترنت',
      timestamp: DateTime.now(),
      context: context,
    );
  }

  factory AppError.server({
    String? message,
    String? messageAr,
    int? statusCode,
    Map<String, dynamic>? context,
  }) {
    return AppError(
      id: _generateErrorId(),
      type: ErrorType.server,
      severity: ErrorSeverity.high,
      message: message ?? 'Server error occurred',
      messageAr: messageAr ?? 'حدث خطأ في الخادم',
      details: statusCode != null ? 'Status Code: $statusCode' : null,
      timestamp: DateTime.now(),
      context: context,
    );
  }

  factory AppError.validation({
    required String field,
    String? message,
    String? messageAr,
    Map<String, dynamic>? context,
  }) {
    return AppError(
      id: _generateErrorId(),
      type: ErrorType.validation,
      severity: ErrorSeverity.low,
      message: message ?? 'Validation error for field: $field',
      messageAr: messageAr ?? 'خطأ في التحقق من البيانات',
      details: 'Field: $field',
      timestamp: DateTime.now(),
      context: context,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'severity': severity.toString().split('.').last,
      'message': message,
      'messageAr': messageAr,
      'details': details,
      'stackTrace': stackTrace,
      'timestamp': timestamp.toIso8601String(),
      'context': context,
      'userId': userId,
      'sessionId': sessionId,
    };
  }

  static String _generateErrorId() {
    return 'ERR_${DateTime.now().millisecondsSinceEpoch}';
  }

  static ErrorType _determineErrorType(Exception exception) {
    if (exception is SocketException) {
      return ErrorType.network;
    } else if (exception is HttpException) {
      return ErrorType.server;
    } else if (exception is FormatException) {
      return ErrorType.validation;
    } else {
      return ErrorType.unknown;
    }
  }

  static Map<String, String> _getErrorMessages(ErrorType type, Exception exception) {
    switch (type) {
      case ErrorType.network:
        return {
          'en': 'Please check your internet connection and try again',
          'ar': 'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى',
        };
      case ErrorType.server:
        return {
          'en': 'Server is temporarily unavailable. Please try again later',
          'ar': 'الخادم غير متاح مؤقتاً. يرجى المحاولة لاحقاً',
        };
      case ErrorType.authentication:
        return {
          'en': 'Authentication failed. Please login again',
          'ar': 'فشل في المصادقة. يرجى تسجيل الدخول مرة أخرى',
        };
      case ErrorType.validation:
        return {
          'en': 'Please check your input and try again',
          'ar': 'يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى',
        };
      case ErrorType.permission:
        return {
          'en': 'You don\'t have permission to perform this action',
          'ar': 'ليس لديك صلاحية لتنفيذ هذا الإجراء',
        };
      case ErrorType.payment:
        return {
          'en': 'Payment processing failed. Please try again',
          'ar': 'فشل في معالجة الدفع. يرجى المحاولة مرة أخرى',
        };
      default:
        return {
          'en': 'An unexpected error occurred',
          'ar': 'حدث خطأ غير متوقع',
        };
    }
  }
}

class ErrorService {
  static const String _errorsKey = 'app_errors';
  static const int _maxStoredErrors = 100;

  // Log error
  static Future<void> logError(AppError error) async {
    try {
      // Store error locally
      await _storeErrorLocally(error);
      
      // Log to console in debug mode
      if (kDebugMode) {
        print('🔴 ERROR [${error.type}]: ${error.message}');
        if (error.details != null) {
          print('Details: ${error.details}');
        }
        if (error.stackTrace != null) {
          print('Stack Trace: ${error.stackTrace}');
        }
      }
      
      // Send to remote logging service in production
      if (kReleaseMode) {
        await _sendToRemoteLogging(error);
      }
    } catch (e) {
      // Fallback logging
      if (kDebugMode) {
        print('Failed to log error: $e');
      }
    }
  }

  // Handle and log exception
  static Future<AppError> handleException(
    Exception exception, {
    ErrorType? type,
    ErrorSeverity severity = ErrorSeverity.medium,
    Map<String, dynamic>? context,
  }) async {
    final error = AppError.fromException(
      exception,
      type: type,
      severity: severity,
      context: context,
    );
    
    await logError(error);
    return error;
  }

  // Get user-friendly error message
  static String getUserMessage(AppError error, {bool useArabic = true}) {
    return useArabic ? error.messageAr : error.message;
  }

  // Get stored errors
  static List<AppError> getStoredErrors() {
    try {
      final errorsString = CacheService.prefs.getString(_errorsKey);
      if (errorsString == null) return [];
      
      final errorsList = json.decode(errorsString) as List<dynamic>;
      return errorsList.map((errorJson) {
        return AppError(
          id: errorJson['id'],
          type: ErrorType.values.firstWhere(
            (e) => e.toString().split('.').last == errorJson['type'],
            orElse: () => ErrorType.unknown,
          ),
          severity: ErrorSeverity.values.firstWhere(
            (e) => e.toString().split('.').last == errorJson['severity'],
            orElse: () => ErrorSeverity.medium,
          ),
          message: errorJson['message'],
          messageAr: errorJson['messageAr'],
          details: errorJson['details'],
          stackTrace: errorJson['stackTrace'],
          timestamp: DateTime.parse(errorJson['timestamp']),
          context: errorJson['context'],
          userId: errorJson['userId'],
          sessionId: errorJson['sessionId'],
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }

  // Clear stored errors
  static Future<void> clearStoredErrors() async {
    await CacheService.prefs.remove(_errorsKey);
  }

  // Get error statistics
  static Map<String, dynamic> getErrorStatistics() {
    final errors = getStoredErrors();
    final now = DateTime.now();
    
    // Group by type
    final byType = <ErrorType, int>{};
    final bySeverity = <ErrorSeverity, int>{};
    final last24Hours = <AppError>[];
    final lastWeek = <AppError>[];
    
    for (final error in errors) {
      byType[error.type] = (byType[error.type] ?? 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] ?? 0) + 1;
      
      final hoursDiff = now.difference(error.timestamp).inHours;
      if (hoursDiff <= 24) {
        last24Hours.add(error);
      }
      
      final daysDiff = now.difference(error.timestamp).inDays;
      if (daysDiff <= 7) {
        lastWeek.add(error);
      }
    }
    
    return {
      'total': errors.length,
      'byType': byType.map((k, v) => MapEntry(k.toString().split('.').last, v)),
      'bySeverity': bySeverity.map((k, v) => MapEntry(k.toString().split('.').last, v)),
      'last24Hours': last24Hours.length,
      'lastWeek': lastWeek.length,
      'criticalErrors': errors.where((e) => e.severity == ErrorSeverity.critical).length,
    };
  }

  // Store error locally
  static Future<void> _storeErrorLocally(AppError error) async {
    try {
      final errors = getStoredErrors();
      errors.insert(0, error);
      
      // Keep only the most recent errors
      if (errors.length > _maxStoredErrors) {
        errors.removeRange(_maxStoredErrors, errors.length);
      }
      
      final errorsJson = errors.map((e) => e.toJson()).toList();
      await CacheService.prefs.setString(_errorsKey, json.encode(errorsJson));
    } catch (e) {
      // Silently fail to avoid infinite error loops
    }
  }

  // Send to remote logging service
  static Future<void> _sendToRemoteLogging(AppError error) async {
    try {
      // This would integrate with services like Crashlytics, Sentry, etc.
      // For now, we'll just simulate the call
      
      // final response = await http.post(
      //   Uri.parse('https://your-logging-service.com/errors'),
      //   headers: {'Content-Type': 'application/json'},
      //   body: json.encode(error.toJson()),
      // );
      
      // if (response.statusCode != 200) {
      //   throw Exception('Failed to send error to remote service');
      // }
    } catch (e) {
      // Silently fail to avoid infinite error loops
    }
  }

  // Common error handlers
  static AppError networkError({Map<String, dynamic>? context}) {
    return AppError.network(context: context);
  }

  static AppError serverError({
    int? statusCode,
    Map<String, dynamic>? context,
  }) {
    return AppError.server(statusCode: statusCode, context: context);
  }

  static AppError validationError({
    required String field,
    String? message,
    String? messageAr,
    Map<String, dynamic>? context,
  }) {
    return AppError.validation(
      field: field,
      message: message,
      messageAr: messageAr,
      context: context,
    );
  }

  // Error recovery suggestions
  static List<String> getRecoverySuggestions(AppError error) {
    switch (error.type) {
      case ErrorType.network:
        return [
          'تحقق من اتصالك بالإنترنت',
          'جرب الاتصال بشبكة Wi-Fi مختلفة',
          'أعد تشغيل التطبيق',
          'تحقق من إعدادات الشبكة',
        ];
      case ErrorType.server:
        return [
          'انتظر قليلاً وحاول مرة أخرى',
          'تحقق من حالة الخدمة',
          'أعد تشغيل التطبيق',
          'تواصل مع الدعم الفني',
        ];
      case ErrorType.authentication:
        return [
          'سجل خروج ثم سجل دخول مرة أخرى',
          'تحقق من بيانات تسجيل الدخول',
          'أعد تعيين كلمة المرور',
          'تواصل مع الدعم الفني',
        ];
      case ErrorType.validation:
        return [
          'تحقق من البيانات المدخلة',
          'تأكد من ملء جميع الحقول المطلوبة',
          'تحقق من تنسيق البيانات',
          'جرب إدخال البيانات مرة أخرى',
        ];
      case ErrorType.payment:
        return [
          'تحقق من بيانات البطاقة',
          'تأكد من وجود رصيد كافي',
          'جرب طريقة دفع أخرى',
          'تواصل مع البنك',
        ];
      default:
        return [
          'أعد تشغيل التطبيق',
          'تحقق من اتصالك بالإنترنت',
          'حدث التطبيق إلى أحدث إصدار',
          'تواصل مع الدعم الفني',
        ];
    }
  }
}
