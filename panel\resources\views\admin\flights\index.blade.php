@extends('layouts.admin')

@section('title', 'إدارة الرحلات الجوية')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-plane"></i>
                        الرحلات الجوية
                    </h3>
                    <div>
                        <a href="{{ route('admin.flights.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة رحلة جديدة
                        </a>
                        <button class="btn btn-info" data-toggle="modal" data-target="#importModal">
                            <i class="fas fa-upload"></i>
                            استيراد رحلات
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-control" id="airline-filter">
                                <option value="">جميع شركات الطيران</option>
                                @foreach($airlines as $airline)
                                    <option value="{{ $airline->id }}">{{ $airline->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="from-airport-filter">
                                <option value="">جميع مطارات المغادرة</option>
                                @foreach($airports as $airport)
                                    <option value="{{ $airport->id }}">{{ $airport->name }} ({{ $airport->code }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="to-airport-filter">
                                <option value="">جميع مطارات الوصول</option>
                                @foreach($airports as $airport)
                                    <option value="{{ $airport->id }}">{{ $airport->name }} ({{ $airport->code }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-filter" placeholder="تاريخ الرحلة">
                        </div>
                    </div>

                    <!-- جدول الرحلات -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="flights-table">
                            <thead>
                                <tr>
                                    <th>رقم الرحلة</th>
                                    <th>شركة الطيران</th>
                                    <th>المسار</th>
                                    <th>تاريخ ووقت المغادرة</th>
                                    <th>تاريخ ووقت الوصول</th>
                                    <th>السعر</th>
                                    <th>المقاعد المتاحة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($flights as $flight)
                                <tr>
                                    <td>
                                        <strong>{{ $flight->flight_number }}</strong>
                                        @if($flight->is_featured)
                                            <span class="badge badge-warning">مميز</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($flight->airline->logo)
                                                <img src="{{ $flight->airline->logo }}" alt="{{ $flight->airline->name }}" 
                                                     class="img-thumbnail me-2" style="width: 30px; height: 30px;">
                                            @endif
                                            {{ $flight->airline->name }}
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $flight->fromAirport->code }}</strong> → <strong>{{ $flight->toAirport->code }}</strong>
                                        </div>
                                        <small class="text-muted">
                                            {{ $flight->fromAirport->city }} → {{ $flight->toAirport->city }}
                                        </small>
                                    </td>
                                    <td>
                                        <div>{{ $flight->departure_date->format('Y-m-d') }}</div>
                                        <small class="text-muted">{{ $flight->departure_time }}</small>
                                    </td>
                                    <td>
                                        <div>{{ $flight->arrival_date->format('Y-m-d') }}</div>
                                        <small class="text-muted">{{ $flight->arrival_time }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ number_format($flight->base_price, 3) }} د.ك</strong>
                                        @if($flight->discount_percentage > 0)
                                            <div>
                                                <small class="text-success">خصم {{ $flight->discount_percentage }}%</small>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $flight->available_seats > 10 ? 'success' : ($flight->available_seats > 0 ? 'warning' : 'danger') }}">
                                            {{ $flight->available_seats }} مقعد
                                        </span>
                                    </td>
                                    <td>
                                        @switch($flight->status)
                                            @case('scheduled')
                                                <span class="badge badge-primary">مجدول</span>
                                                @break
                                            @case('boarding')
                                                <span class="badge badge-info">صعود</span>
                                                @break
                                            @case('departed')
                                                <span class="badge badge-warning">مغادر</span>
                                                @break
                                            @case('arrived')
                                                <span class="badge badge-success">وصل</span>
                                                @break
                                            @case('cancelled')
                                                <span class="badge badge-danger">ملغي</span>
                                                @break
                                            @case('delayed')
                                                <span class="badge badge-secondary">متأخر</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.flights.show', $flight->id) }}" 
                                               class="btn btn-sm btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.flights.edit', $flight->id) }}" 
                                               class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('admin.flights.seats', $flight->id) }}" 
                                               class="btn btn-sm btn-success" title="إدارة المقاعد">
                                                <i class="fas fa-chair"></i>
                                            </a>
                                            <button class="btn btn-sm btn-danger" 
                                                    onclick="deleteFlight({{ $flight->id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $flights->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد رحلات من ملف Excel</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.flights.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>ملف Excel</label>
                        <input type="file" name="file" class="form-control" accept=".xlsx,.xls" required>
                        <small class="form-text text-muted">
                            يجب أن يحتوي الملف على الأعمدة: flight_number, airline_id, from_airport, to_airport, departure_date, departure_time, arrival_date, arrival_time, base_price
                        </small>
                    </div>
                    <div class="form-group">
                        <a href="{{ route('admin.flights.template') }}" class="btn btn-link">
                            <i class="fas fa-download"></i>
                            تحميل قالب Excel
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">استيراد</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#flights-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 3, "asc" ]],
        "pageLength": 25
    });

    // Filter functionality
    $('#airline-filter, #from-airport-filter, #to-airport-filter, #date-filter').on('change', function() {
        filterFlights();
    });
});

function filterFlights() {
    const airline = $('#airline-filter').val();
    const fromAirport = $('#from-airport-filter').val();
    const toAirport = $('#to-airport-filter').val();
    const date = $('#date-filter').val();
    
    const params = new URLSearchParams();
    if (airline) params.append('airline', airline);
    if (fromAirport) params.append('from_airport', fromAirport);
    if (toAirport) params.append('to_airport', toAirport);
    if (date) params.append('date', date);
    
    window.location.href = '{{ route("admin.flights.index") }}?' + params.toString();
}

function deleteFlight(id) {
    if (confirm('هل أنت متأكد من حذف هذه الرحلة؟')) {
        $.ajax({
            url: '/admin/flights/' + id,
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء الحذف');
                }
            }
        });
    }
}
</script>
@endsection
