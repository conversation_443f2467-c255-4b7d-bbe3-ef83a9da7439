{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "eluceo/ical": "^0.15.1", "fruitcake/laravel-cors": "^2.0", "intervention/image": "^2.4", "johngrogg/ics-parser": "^2.1", "kalnoy/nestedset": "^6", "laravel/fortify": "^1.10", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^9.19", "laravel/sanctum": "^3.0", "laravel/tinker": "^2.7", "laravel/socialite": "^5.0", "laravel/ui": "^3.0", "league/flysystem-aws-s3-v3": "^3.0", "league/omnipay": "^3", "maatwebsite/excel": "^3.1", "mews/purifier": "^3.3", "munafio/chatify": "^1.3.4", "omnipay/migs": "~3.0", "omnipay/paypal": "^3.0", "omnipay/stripe": "^3.0", "propaganistas/laravel-phone": "^4.2", "pusher/pusher-php-server": "^7.0", "rachidlaasri/laravel-installer": "^4.0", "rap2hpoutre/laravel-log-viewer": "^2.2.0", "simplesoftwareio/simple-qrcode": "^4.2.0", "stripe/stripe-php": "^7.113", "spatie/laravel-google-cloud-storage": "^2.0.3", "symfony/http-client": "^6.0", "symfony/mailgun-mailer": "^6.0", "tijsverkoyen/css-to-inline-styles": "^2.2", "payrexx/payrexx": "dev-master"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Plugins\\": "plugins/", "Custom\\": "custom/", "Modules\\": "modules/", "Themes\\": "themes/"}, "files": ["app/Helpers/AppHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}