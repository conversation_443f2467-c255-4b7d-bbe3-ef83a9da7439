<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Venue;
use App\Models\TicketBooking;
use App\Models\TicketCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class EventApiController extends Controller
{
    /**
     * Get list of events with search and filters
     */
    public function index(Request $request)
    {
        try {
            $query = Event::with(['venue', 'availableTicketCategories'])
                          ->whereIn('status', ['upcoming', 'ongoing']);

            // Search filters
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('title_ar', 'like', "%{$search}%");
                });
            }

            if ($request->filled('event_type')) {
                $query->where('event_type', $request->event_type);
            }

            if ($request->filled('venue_id')) {
                $query->where('venue_id', $request->venue_id);
            }

            if ($request->filled('city')) {
                $query->whereHas('venue', function($q) use ($request) {
                    $q->where('city', $request->city);
                });
            }

            if ($request->filled('min_price')) {
                $query->where('price', '>=', $request->min_price);
            }

            if ($request->filled('max_price')) {
                $query->where('price', '<=', $request->max_price);
            }

            if ($request->filled('is_featured')) {
                $query->where('is_featured', $request->boolean('is_featured'));
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            
            if (in_array($sortBy, ['created_at', 'price', 'title'])) {
                $query->orderBy($sortBy, $sortOrder);
            }

            $events = $query->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => $events->items(),
                'pagination' => [
                    'current_page' => $events->currentPage(),
                    'last_page' => $events->lastPage(),
                    'per_page' => $events->perPage(),
                    'total' => $events->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الفعاليات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get event details
     */
    public function show($id)
    {
        try {
            $event = Event::with([
                'venue', 
                'ticketCategories' => function($query) {
                    $query->where('available_quantity', '>', 0);
                },
                'eventDates'
            ])->find($id);

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفعالية غير موجودة'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $event
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب تفاصيل الفعالية',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get venues list
     */
    public function venues(Request $request)
    {
        try {
            $query = Venue::active()->orderBy('name');

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('name_ar', 'like', "%{$search}%")
                      ->orWhere('city', 'like', "%{$search}%");
                });
            }

            if ($request->filled('city')) {
                $query->where('city', $request->city);
            }

            if ($request->filled('country')) {
                $query->where('country', $request->country);
            }

            $venues = $query->get();

            return response()->json([
                'success' => true,
                'data' => $venues
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الأماكن',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Book event tickets
     */
    public function book(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'event_id' => 'required|exists:events,id',
                'ticket_category_id' => 'required|exists:ticket_categories,id',
                'quantity' => 'required|integer|min:1|max:10',
                'special_requests' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $event = Event::find($request->event_id);
            $ticketCategory = TicketCategory::find($request->ticket_category_id);
            
            if (!$event || !in_array($event->status, ['upcoming', 'ongoing'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفعالية غير متاحة للحجز'
                ], 400);
            }

            if (!$ticketCategory || $ticketCategory->event_id !== $event->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'فئة التذاكر غير صحيحة'
                ], 400);
            }

            if ($ticketCategory->available_quantity < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'عدد التذاكر المطلوب غير متاح'
                ], 400);
            }

            $totalPrice = $ticketCategory->price * $request->quantity;

            // Create booking
            $booking = TicketBooking::create([
                'user_id' => auth()->id(),
                'event_id' => $request->event_id,
                'ticket_category_id' => $request->ticket_category_id,
                'quantity' => $request->quantity,
                'total_price' => $totalPrice,
                'booking_reference' => TicketBooking::generateBookingReference(),
                'status' => 'pending',
                'payment_status' => 'pending',
                'booking_date' => now(),
                'special_requests' => $request->special_requests
            ]);

            // Reserve tickets
            $ticketCategory->reserveTickets($request->quantity);

            // Generate QR code
            $booking->generateQrCode();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الحجز بنجاح',
                'data' => [
                    'booking_reference' => $booking->booking_reference,
                    'total_price' => $totalPrice,
                    'booking_id' => $booking->id,
                    'qr_code' => $booking->qr_code
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في إنشاء الحجز',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's ticket bookings
     */
    public function myBookings(Request $request)
    {
        try {
            $query = TicketBooking::with(['event.venue', 'ticketCategory'])
                                 ->where('user_id', auth()->id());

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            $bookings = $query->orderBy('booking_date', 'desc')
                             ->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => $bookings->items(),
                'pagination' => [
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الحجوزات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify ticket QR code
     */
    public function verifyTicket(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'booking_reference' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'رقم الحجز مطلوب',
                    'errors' => $validator->errors()
                ], 422);
            }

            $booking = TicketBooking::with(['event', 'ticketCategory', 'user'])
                                   ->where('booking_reference', $request->booking_reference)
                                   ->first();

            if (!$booking) {
                return response()->json([
                    'success' => false,
                    'message' => 'الحجز غير موجود'
                ], 404);
            }

            if (!$booking->isConfirmed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'الحجز غير مؤكد'
                ], 400);
            }

            if ($booking->isUsed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'تم استخدام التذكرة مسبقاً',
                    'data' => [
                        'used_at' => $booking->used_at
                    ]
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'التذكرة صالحة',
                'data' => [
                    'booking' => $booking,
                    'can_use' => true
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في التحقق من التذكرة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark ticket as used
     */
    public function useTicket(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'booking_reference' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'رقم الحجز مطلوب',
                    'errors' => $validator->errors()
                ], 422);
            }

            $booking = TicketBooking::where('booking_reference', $request->booking_reference)
                                   ->first();

            if (!$booking || !$booking->isConfirmed() || $booking->isUsed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'التذكرة غير صالحة للاستخدام'
                ], 400);
            }

            $booking->markAsUsed();

            return response()->json([
                'success' => true,
                'message' => 'تم استخدام التذكرة بنجاح',
                'data' => [
                    'used_at' => $booking->used_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في استخدام التذكرة',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
