import 'dart:convert';
import 'package:http/http.dart' as http;

class KuwaitWeatherModel {
  final double temperature;
  final double feelsLike;
  final int humidity;
  final double windSpeed;
  final String windDirection;
  final String condition;
  final String conditionAr;
  final String icon;
  final double visibility;
  final int uvIndex;
  final DateTime sunrise;
  final DateTime sunset;
  final List<WeatherForecast> forecast;
  final bool isDustStorm;
  final String airQuality;
  final String airQualityAr;

  KuwaitWeatherModel({
    required this.temperature,
    required this.feelsLike,
    required this.humidity,
    required this.windSpeed,
    required this.windDirection,
    required this.condition,
    required this.conditionAr,
    required this.icon,
    required this.visibility,
    required this.uvIndex,
    required this.sunrise,
    required this.sunset,
    required this.forecast,
    required this.isDustStorm,
    required this.airQuality,
    required this.airQualityAr,
  });

  factory KuwaitWeatherModel.fromJson(Map<String, dynamic> json) {
    return KuwaitWeatherModel(
      temperature: json['temperature']?.toDouble() ?? 0.0,
      feelsLike: json['feels_like']?.toDouble() ?? 0.0,
      humidity: json['humidity'] ?? 0,
      windSpeed: json['wind_speed']?.toDouble() ?? 0.0,
      windDirection: json['wind_direction'] ?? '',
      condition: json['condition'] ?? '',
      conditionAr: json['condition_ar'] ?? '',
      icon: json['icon'] ?? '',
      visibility: json['visibility']?.toDouble() ?? 0.0,
      uvIndex: json['uv_index'] ?? 0,
      sunrise: DateTime.parse(json['sunrise'] ?? DateTime.now().toIso8601String()),
      sunset: DateTime.parse(json['sunset'] ?? DateTime.now().toIso8601String()),
      forecast: (json['forecast'] as List<dynamic>?)
          ?.map((item) => WeatherForecast.fromJson(item))
          .toList() ?? [],
      isDustStorm: json['is_dust_storm'] ?? false,
      airQuality: json['air_quality'] ?? '',
      airQualityAr: json['air_quality_ar'] ?? '',
    );
  }

  String get temperatureDisplay => '${temperature.round()}°C';
  String get feelsLikeDisplay => '${feelsLike.round()}°C';
  String get humidityDisplay => '$humidity%';
  String get windSpeedDisplay => '${windSpeed.round()} km/h';
  String get visibilityDisplay => '${visibility.round()} km';

  bool get isHot => temperature > 40;
  bool get isVeryHot => temperature > 45;
  bool get isExtremelHot => temperature > 50;
  
  String get heatWarningAr {
    if (isExtremelHot) return 'تحذير: حرارة شديدة جداً - تجنب الخروج';
    if (isVeryHot) return 'تحذير: حرارة شديدة - احذر من ضربة الشمس';
    if (isHot) return 'تنبيه: طقس حار - اشرب الماء بكثرة';
    return '';
  }

  String get dustWarningAr {
    if (isDustStorm) return 'تحذير: عاصفة ترابية - تجنب الخروج';
    if (visibility < 5) return 'تنبيه: ضعف في الرؤية بسبب الغبار';
    return '';
  }
}

class WeatherForecast {
  final DateTime date;
  final double maxTemp;
  final double minTemp;
  final String condition;
  final String conditionAr;
  final String icon;
  final int humidity;
  final double windSpeed;
  final bool isDustStorm;

  WeatherForecast({
    required this.date,
    required this.maxTemp,
    required this.minTemp,
    required this.condition,
    required this.conditionAr,
    required this.icon,
    required this.humidity,
    required this.windSpeed,
    required this.isDustStorm,
  });

  factory WeatherForecast.fromJson(Map<String, dynamic> json) {
    return WeatherForecast(
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      maxTemp: json['max_temp']?.toDouble() ?? 0.0,
      minTemp: json['min_temp']?.toDouble() ?? 0.0,
      condition: json['condition'] ?? '',
      conditionAr: json['condition_ar'] ?? '',
      icon: json['icon'] ?? '',
      humidity: json['humidity'] ?? 0,
      windSpeed: json['wind_speed']?.toDouble() ?? 0.0,
      isDustStorm: json['is_dust_storm'] ?? false,
    );
  }

  String get maxTempDisplay => '${maxTemp.round()}°';
  String get minTempDisplay => '${minTemp.round()}°';
  String get dayName {
    final days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    return days[date.weekday % 7];
  }
}

class PrayerTimesModel {
  final DateTime fajr;
  final DateTime sunrise;
  final DateTime dhuhr;
  final DateTime asr;
  final DateTime maghrib;
  final DateTime isha;
  final DateTime? nextPrayer;
  final String nextPrayerName;
  final String nextPrayerNameAr;

  PrayerTimesModel({
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
    this.nextPrayer,
    required this.nextPrayerName,
    required this.nextPrayerNameAr,
  });

  factory PrayerTimesModel.fromJson(Map<String, dynamic> json) {
    return PrayerTimesModel(
      fajr: DateTime.parse(json['fajr'] ?? DateTime.now().toIso8601String()),
      sunrise: DateTime.parse(json['sunrise'] ?? DateTime.now().toIso8601String()),
      dhuhr: DateTime.parse(json['dhuhr'] ?? DateTime.now().toIso8601String()),
      asr: DateTime.parse(json['asr'] ?? DateTime.now().toIso8601String()),
      maghrib: DateTime.parse(json['maghrib'] ?? DateTime.now().toIso8601String()),
      isha: DateTime.parse(json['isha'] ?? DateTime.now().toIso8601String()),
      nextPrayer: json['next_prayer'] != null 
          ? DateTime.parse(json['next_prayer']) 
          : null,
      nextPrayerName: json['next_prayer_name'] ?? '',
      nextPrayerNameAr: json['next_prayer_name_ar'] ?? '',
    );
  }

  String formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  String get fajrTime => formatTime(fajr);
  String get sunriseTime => formatTime(sunrise);
  String get dhuhrTime => formatTime(dhuhr);
  String get asrTime => formatTime(asr);
  String get maghribTime => formatTime(maghrib);
  String get ishaTime => formatTime(isha);

  Duration? get timeToNextPrayer {
    if (nextPrayer == null) return null;
    final now = DateTime.now();
    return nextPrayer!.difference(now);
  }

  String get timeToNextPrayerDisplay {
    final duration = timeToNextPrayer;
    if (duration == null) return '';
    
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    
    if (hours > 0) {
      return '$hours ساعة و $minutes دقيقة';
    } else {
      return '$minutes دقيقة';
    }
  }
}

class KuwaitWeatherService {
  static const String _baseUrl = 'https://api.openweathermap.org/data/2.5';
  static const String _apiKey = 'YOUR_API_KEY'; // Replace with actual API key
  static const double _kuwaitLat = 29.3759;
  static const double _kuwaitLon = 47.9774;

  static Future<KuwaitWeatherModel> getCurrentWeather() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/weather?lat=$_kuwaitLat&lon=$_kuwaitLon&appid=$_apiKey&units=metric&lang=ar'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseWeatherData(data);
      } else {
        throw Exception('Failed to load weather data');
      }
    } catch (e) {
      // Return mock data for development
      return _getMockWeatherData();
    }
  }

  static Future<List<WeatherForecast>> getWeatherForecast() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/forecast?lat=$_kuwaitLat&lon=$_kuwaitLon&appid=$_apiKey&units=metric&lang=ar'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseForecastData(data);
      } else {
        throw Exception('Failed to load forecast data');
      }
    } catch (e) {
      // Return mock data for development
      return _getMockForecastData();
    }
  }

  static Future<PrayerTimesModel> getPrayerTimes() async {
    try {
      // This would integrate with a prayer times API
      // For now, return mock data
      return _getMockPrayerTimes();
    } catch (e) {
      return _getMockPrayerTimes();
    }
  }

  static KuwaitWeatherModel _parseWeatherData(Map<String, dynamic> data) {
    final main = data['main'];
    final weather = data['weather'][0];
    final wind = data['wind'];
    final sys = data['sys'];

    return KuwaitWeatherModel(
      temperature: main['temp']?.toDouble() ?? 0.0,
      feelsLike: main['feels_like']?.toDouble() ?? 0.0,
      humidity: main['humidity'] ?? 0,
      windSpeed: wind['speed']?.toDouble() ?? 0.0,
      windDirection: _getWindDirection(wind['deg'] ?? 0),
      condition: weather['main'] ?? '',
      conditionAr: _getConditionArabic(weather['main'] ?? ''),
      icon: weather['icon'] ?? '',
      visibility: (data['visibility'] ?? 10000) / 1000.0,
      uvIndex: 0, // Would need UV API
      sunrise: DateTime.fromMillisecondsSinceEpoch((sys['sunrise'] ?? 0) * 1000),
      sunset: DateTime.fromMillisecondsSinceEpoch((sys['sunset'] ?? 0) * 1000),
      forecast: [],
      isDustStorm: _isDustStorm(weather['main'] ?? ''),
      airQuality: 'جيد',
      airQualityAr: 'جيد',
    );
  }

  static List<WeatherForecast> _parseForecastData(Map<String, dynamic> data) {
    final list = data['list'] as List<dynamic>;
    return list.take(5).map((item) {
      final main = item['main'];
      final weather = item['weather'][0];
      final wind = item['wind'];

      return WeatherForecast(
        date: DateTime.fromMillisecondsSinceEpoch(item['dt'] * 1000),
        maxTemp: main['temp_max']?.toDouble() ?? 0.0,
        minTemp: main['temp_min']?.toDouble() ?? 0.0,
        condition: weather['main'] ?? '',
        conditionAr: _getConditionArabic(weather['main'] ?? ''),
        icon: weather['icon'] ?? '',
        humidity: main['humidity'] ?? 0,
        windSpeed: wind['speed']?.toDouble() ?? 0.0,
        isDustStorm: _isDustStorm(weather['main'] ?? ''),
      );
    }).toList();
  }

  static String _getConditionArabic(String condition) {
    switch (condition.toLowerCase()) {
      case 'clear':
        return 'صافي';
      case 'clouds':
        return 'غائم';
      case 'rain':
        return 'ممطر';
      case 'thunderstorm':
        return 'عاصف';
      case 'snow':
        return 'ثلجي';
      case 'mist':
      case 'fog':
        return 'ضبابي';
      case 'dust':
      case 'sand':
        return 'مغبر';
      case 'haze':
        return 'ضبابي';
      default:
        return condition;
    }
  }

  static String _getWindDirection(int degrees) {
    if (degrees >= 337.5 || degrees < 22.5) return 'شمال';
    if (degrees >= 22.5 && degrees < 67.5) return 'شمال شرق';
    if (degrees >= 67.5 && degrees < 112.5) return 'شرق';
    if (degrees >= 112.5 && degrees < 157.5) return 'جنوب شرق';
    if (degrees >= 157.5 && degrees < 202.5) return 'جنوب';
    if (degrees >= 202.5 && degrees < 247.5) return 'جنوب غرب';
    if (degrees >= 247.5 && degrees < 292.5) return 'غرب';
    if (degrees >= 292.5 && degrees < 337.5) return 'شمال غرب';
    return 'غير محدد';
  }

  static bool _isDustStorm(String condition) {
    return condition.toLowerCase().contains('dust') || 
           condition.toLowerCase().contains('sand');
  }

  // Mock data for development
  static KuwaitWeatherModel _getMockWeatherData() {
    final now = DateTime.now();
    return KuwaitWeatherModel(
      temperature: 42.0,
      feelsLike: 48.0,
      humidity: 65,
      windSpeed: 15.0,
      windDirection: 'شمال غرب',
      condition: 'Clear',
      conditionAr: 'صافي',
      icon: '01d',
      visibility: 8.0,
      uvIndex: 9,
      sunrise: DateTime(now.year, now.month, now.day, 5, 30),
      sunset: DateTime(now.year, now.month, now.day, 18, 45),
      forecast: _getMockForecastData(),
      isDustStorm: false,
      airQuality: 'متوسط',
      airQualityAr: 'متوسط',
    );
  }

  static List<WeatherForecast> _getMockForecastData() {
    final now = DateTime.now();
    return List.generate(5, (index) {
      return WeatherForecast(
        date: now.add(Duration(days: index + 1)),
        maxTemp: 40.0 + (index * 2),
        minTemp: 28.0 + index,
        condition: index % 2 == 0 ? 'Clear' : 'Clouds',
        conditionAr: index % 2 == 0 ? 'صافي' : 'غائم',
        icon: index % 2 == 0 ? '01d' : '02d',
        humidity: 60 + (index * 5),
        windSpeed: 10.0 + index,
        isDustStorm: index == 2,
      );
    });
  }

  static PrayerTimesModel _getMockPrayerTimes() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    return PrayerTimesModel(
      fajr: today.add(Duration(hours: 4, minutes: 30)),
      sunrise: today.add(Duration(hours: 5, minutes: 45)),
      dhuhr: today.add(Duration(hours: 11, minutes: 50)),
      asr: today.add(Duration(hours: 15, minutes: 20)),
      maghrib: today.add(Duration(hours: 18, minutes: 30)),
      isha: today.add(Duration(hours: 19, minutes: 45)),
      nextPrayer: today.add(Duration(hours: 15, minutes: 20)),
      nextPrayerName: 'Asr',
      nextPrayerNameAr: 'العصر',
    );
  }

  static List<String> getWeatherTips(KuwaitWeatherModel weather) {
    List<String> tips = [];

    if (weather.isExtremelHot) {
      tips.addAll([
        'تجنب الخروج في فترة الظهيرة (11 ص - 4 م)',
        'اشرب الماء بكثرة حتى لو لم تشعر بالعطش',
        'ارتد ملابس فاتحة اللون وفضفاضة',
        'استخدم واقي الشمس بعامل حماية عالي',
      ]);
    } else if (weather.isVeryHot) {
      tips.addAll([
        'قلل من الأنشطة الخارجية',
        'اشرب السوائل بانتظام',
        'ابق في الظل قدر الإمكان',
      ]);
    }

    if (weather.isDustStorm) {
      tips.addAll([
        'أغلق النوافذ والأبواب',
        'تجنب القيادة إلا للضرورة',
        'ارتد كمامة عند الخروج',
        'استخدم مرشح الهواء في المنزل',
      ]);
    }

    if (weather.uvIndex > 7) {
      tips.addAll([
        'استخدم واقي الشمس كل ساعتين',
        'ارتد نظارات شمسية',
        'ابحث عن الظل',
      ]);
    }

    if (weather.windSpeed > 25) {
      tips.add('احذر من الرياح القوية عند القيادة');
    }

    return tips;
  }
}
