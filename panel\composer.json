{"name": "amrdev/travel-offers", "type": "project", "description": "Travel Offers Platform - تطبيق عروض السفر والسياحة by AmrDev", "keywords": ["travel", "tourism", "offers", "umrah", "hajj", "flights", "hotels"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1.0", "ext-curl": "*", "ext-openssl": "*", "ext-zip": "*", "cerbero/json-parser": "^1.1", "dacoto/laravel-wizard-installer": "^1.0", "devaslanphp/auto-translate": "*", "doctrine/dbal": "^3.6", "flutterwavedev/flutterwave-v3": "^1.0", "google/apiclient": "^2.16", "guzzlehttp/guzzle": "^7.2", "imlolman/phonepe-php-sdk": "^0.0.2", "kingflamez/laravelrave": "4.2.1", "kornrunner/blurhash": "^1.2", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.7", "laravel/ui": "^4.0", "laravelcollective/html": "^6.3", "league/flysystem-aws-s3-v3": "^3.0", "nesbot/carbon": "^2.72.3", "php-webdriver/webdriver": "^1.14", "rap2hpoutre/laravel-log-viewer": "^2.4", "razorpay/razorpay": "2.*", "spatie/image": "^2.2", "spatie/laravel-ignition": "^2.0", "spatie/laravel-package-tools": "^1.16", "spatie/laravel-permission": "^6.4", "staudenmeir/laravel-adjacency-list": "^1.0", "stripe/stripe-php": "^13.14", "sunra/php-simple-html-dom-parser": "^1.5", "symfony/dom-crawler": "^6.3", "twilio/sdk": "^8.4", "unicodeveloper/laravel-paystack": "^1.2", "weidner/goutte": "^2.3", "simplesoftwareio/simple-qrcode": "^4.2"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "barryvdh/laravel-ide-helper": "^2.13", "beyondcode/laravel-query-detector": "^1.8", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "roave/security-advisories": "dev-latest"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "pre-autoload-dump": "Google\\Task\\Composer::cleanup"}, "extra": {"laravel": {"dont-discover": []}, "google/apiclient-services": ["FirebaseCloudMessaging"]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}