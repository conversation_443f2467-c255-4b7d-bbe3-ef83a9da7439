import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:travel_offers/data/model/event/event_model.dart';
import 'package:travel_offers/utils/api_constants.dart';

class EventService {
  static const String baseUrl = ApiConstants.baseUrl;

  // Get events with filters
  static Future<EventResponse> getEvents({
    String? search,
    String? eventType,
    String? venueId,
    String? city,
    double? minPrice,
    double? maxPrice,
    bool? isFeatured,
    String sortBy = 'created_at',
    String sortOrder = 'desc',
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'per_page': perPage.toString(),
        'sort_by': sortBy,
        'sort_order': sortOrder,
      };

      if (search != null) queryParams['search'] = search;
      if (eventType != null) queryParams['event_type'] = eventType;
      if (venueId != null) queryParams['venue_id'] = venueId;
      if (city != null) queryParams['city'] = city;
      if (minPrice != null) queryParams['min_price'] = minPrice.toString();
      if (maxPrice != null) queryParams['max_price'] = maxPrice.toString();
      if (isFeatured != null) queryParams['is_featured'] = isFeatured.toString();

      final uri = Uri.parse('$baseUrl/api/events').replace(queryParameters: queryParams);
      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return EventResponse.fromJson(data);
      } else {
        throw Exception('Failed to load events: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading events: $e');
    }
  }

  // Get event details
  static Future<EventModel> getEventDetails(int eventId) async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/api/events/$eventId'));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return EventModel.fromJson(data['data']);
        } else {
          throw Exception(data['message'] ?? 'Failed to load event details');
        }
      } else {
        throw Exception('Failed to load event details: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading event details: $e');
    }
  }

  // Get venues list
  static Future<List<VenueModel>> getVenues({
    String? search,
    String? city,
    String? country,
  }) async {
    try {
      final Map<String, String> queryParams = {};
      if (search != null) queryParams['search'] = search;
      if (city != null) queryParams['city'] = city;
      if (country != null) queryParams['country'] = country;

      final uri = Uri.parse('$baseUrl/api/events/venues/list').replace(queryParameters: queryParams);
      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return (data['data'] as List)
              .map((venue) => VenueModel.fromJson(venue))
              .toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to load venues');
        }
      } else {
        throw Exception('Failed to load venues: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading venues: $e');
    }
  }

  // Book event tickets
  static Future<TicketBookingResponse> bookTickets({
    required String token,
    required int eventId,
    required int ticketCategoryId,
    required int quantity,
    String? specialRequests,
  }) async {
    try {
      final Map<String, dynamic> requestBody = {
        'event_id': eventId,
        'ticket_category_id': ticketCategoryId,
        'quantity': quantity,
        if (specialRequests != null) 'special_requests': specialRequests,
      };

      final response = await http.post(
        Uri.parse('$baseUrl/api/events/book'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestBody),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200 && data['success']) {
        return TicketBookingResponse.fromJson(data);
      } else {
        throw Exception(data['message'] ?? 'Failed to book tickets');
      }
    } catch (e) {
      throw Exception('Error booking tickets: $e');
    }
  }

  // Get user's ticket bookings
  static Future<List<TicketBookingModel>> getMyBookings({
    required String token,
    String? status,
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'per_page': perPage.toString(),
      };
      if (status != null) queryParams['status'] = status;

      final uri = Uri.parse('$baseUrl/api/events/my-bookings').replace(queryParameters: queryParams);
      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return (data['data'] as List)
              .map((booking) => TicketBookingModel.fromJson(booking))
              .toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to load bookings');
        }
      } else {
        throw Exception('Failed to load bookings: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading bookings: $e');
    }
  }

  // Verify ticket
  static Future<TicketVerificationResponse> verifyTicket(String bookingReference) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/events/verify-ticket'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'booking_reference': bookingReference,
        }),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200 && data['success']) {
        return TicketVerificationResponse.fromJson(data);
      } else {
        throw Exception(data['message'] ?? 'Failed to verify ticket');
      }
    } catch (e) {
      throw Exception('Error verifying ticket: $e');
    }
  }

  // Use ticket
  static Future<TicketUsageResponse> useTicket({
    required String token,
    required String bookingReference,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/events/use-ticket'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'booking_reference': bookingReference,
        }),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200 && data['success']) {
        return TicketUsageResponse.fromJson(data);
      } else {
        throw Exception(data['message'] ?? 'Failed to use ticket');
      }
    } catch (e) {
      throw Exception('Error using ticket: $e');
    }
  }
}

class EventResponse {
  final List<EventModel> events;
  final PaginationData pagination;

  EventResponse({
    required this.events,
    required this.pagination,
  });

  factory EventResponse.fromJson(Map<String, dynamic> json) {
    return EventResponse(
      events: (json['data'] as List)
          .map((event) => EventModel.fromJson(event))
          .toList(),
      pagination: PaginationData.fromJson(json['pagination']),
    );
  }
}

class PaginationData {
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;

  PaginationData({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
  });

  factory PaginationData.fromJson(Map<String, dynamic> json) {
    return PaginationData(
      currentPage: json['current_page'] ?? 1,
      lastPage: json['last_page'] ?? 1,
      perPage: json['per_page'] ?? 20,
      total: json['total'] ?? 0,
    );
  }
}

class TicketBookingResponse {
  final String bookingReference;
  final double totalPrice;
  final int bookingId;
  final String? qrCode;
  final String message;

  TicketBookingResponse({
    required this.bookingReference,
    required this.totalPrice,
    required this.bookingId,
    this.qrCode,
    required this.message,
  });

  factory TicketBookingResponse.fromJson(Map<String, dynamic> json) {
    return TicketBookingResponse(
      bookingReference: json['data']['booking_reference'] ?? '',
      totalPrice: (json['data']['total_price'] ?? 0).toDouble(),
      bookingId: json['data']['booking_id'] ?? 0,
      qrCode: json['data']['qr_code'],
      message: json['message'] ?? '',
    );
  }
}

class TicketBookingModel {
  final int id;
  final String bookingReference;
  final double totalPrice;
  final int quantity;
  final String status;
  final String paymentStatus;
  final DateTime bookingDate;
  final String? qrCode;
  final EventModel event;
  final TicketCategoryModel ticketCategory;

  TicketBookingModel({
    required this.id,
    required this.bookingReference,
    required this.totalPrice,
    required this.quantity,
    required this.status,
    required this.paymentStatus,
    required this.bookingDate,
    this.qrCode,
    required this.event,
    required this.ticketCategory,
  });

  factory TicketBookingModel.fromJson(Map<String, dynamic> json) {
    return TicketBookingModel(
      id: json['id'] ?? 0,
      bookingReference: json['booking_reference'] ?? '',
      totalPrice: (json['total_price'] ?? 0).toDouble(),
      quantity: json['quantity'] ?? 0,
      status: json['status'] ?? '',
      paymentStatus: json['payment_status'] ?? '',
      bookingDate: DateTime.parse(json['booking_date']),
      qrCode: json['qr_code'],
      event: EventModel.fromJson(json['event'] ?? {}),
      ticketCategory: TicketCategoryModel.fromJson(json['ticket_category'] ?? {}),
    );
  }
}

class TicketVerificationResponse {
  final bool canUse;
  final String message;
  final TicketBookingModel? booking;
  final DateTime? usedAt;

  TicketVerificationResponse({
    required this.canUse,
    required this.message,
    this.booking,
    this.usedAt,
  });

  factory TicketVerificationResponse.fromJson(Map<String, dynamic> json) {
    return TicketVerificationResponse(
      canUse: json['data']['can_use'] ?? false,
      message: json['message'] ?? '',
      booking: json['data']['booking'] != null
          ? TicketBookingModel.fromJson(json['data']['booking'])
          : null,
      usedAt: json['data']['used_at'] != null
          ? DateTime.parse(json['data']['used_at'])
          : null,
    );
  }
}

class TicketUsageResponse {
  final DateTime usedAt;
  final String message;

  TicketUsageResponse({
    required this.usedAt,
    required this.message,
  });

  factory TicketUsageResponse.fromJson(Map<String, dynamic> json) {
    return TicketUsageResponse(
      usedAt: DateTime.parse(json['data']['used_at']),
      message: json['message'] ?? '',
    );
  }
}
