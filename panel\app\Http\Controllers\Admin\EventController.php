<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Venue;
use App\Models\TicketCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class EventController extends Controller
{
    /**
     * Display a listing of events
     */
    public function index(Request $request)
    {
        $query = Event::with(['venue', 'ticketCategories']);

        // Search filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('title_ar', 'like', "%{$search}%");
            });
        }

        if ($request->filled('event_type')) {
            $query->where('event_type', $request->event_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('venue_id')) {
            $query->where('venue_id', $request->venue_id);
        }

        $events = $query->latest()->paginate(25);
        $venues = Venue::active()->get();
        $categories = EventTerm::all();

        // Statistics
        $totalEvents = Event::count();
        $activeEvents = Event::where('status', 'upcoming')->orWhere('status', 'ongoing')->count();
        $soldTickets = IndividualTicket::where('status', 'sold')->count();
        $totalRevenue = TicketBooking::where('payment_status', 'paid')->sum('total_amount');

        return view('admin.events.index', compact(
            'events', 'venues', 'categories', 'totalEvents',
            'activeEvents', 'soldTickets', 'totalRevenue'
        ));
    }

    /**
     * Show the form for creating a new event
     */
    public function create()
    {
        $venues = Venue::active()->get();
        
        return view('admin.events.create', compact('venues'));
    }

    /**
     * Store a newly created event
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'content' => 'required|string',
            'content_ar' => 'nullable|string',
            'venue_id' => 'required|exists:venues,id',
            'event_type' => 'required|in:football,basketball,concert,conference,other',
            'organizer' => 'nullable|string|max:255',
            'duration' => 'nullable|integer|min:1',
            'start_time' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'is_featured' => 'boolean',
            'status' => 'required|in:upcoming,ongoing,completed,cancelled'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $event = Event::create([
            'title' => $request->title,
            'title_ar' => $request->title_ar,
            'slug' => Str::slug($request->title),
            'content' => $request->content,
            'content_ar' => $request->content_ar,
            'venue_id' => $request->venue_id,
            'event_type' => $request->event_type,
            'organizer' => $request->organizer,
            'duration' => $request->duration,
            'start_time' => $request->start_time,
            'price' => $request->price,
            'sale_price' => $request->sale_price,
            'is_featured' => $request->boolean('is_featured'),
            'status' => $request->status,
            'create_user' => auth()->id()
        ]);

        return redirect()->route('admin.events.index')
                        ->with('success', 'تم إنشاء الفعالية بنجاح');
    }

    /**
     * Display the specified event
     */
    public function show(Event $event)
    {
        $event->load(['venue', 'ticketCategories', 'ticketBookings', 'eventDates']);
        
        return view('admin.events.show', compact('event'));
    }

    /**
     * Show the form for editing the specified event
     */
    public function edit(Event $event)
    {
        $venues = Venue::active()->get();
        
        return view('admin.events.edit', compact('event', 'venues'));
    }

    /**
     * Update the specified event
     */
    public function update(Request $request, Event $event)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'content' => 'required|string',
            'content_ar' => 'nullable|string',
            'venue_id' => 'required|exists:venues,id',
            'event_type' => 'required|in:football,basketball,concert,conference,other',
            'organizer' => 'nullable|string|max:255',
            'duration' => 'nullable|integer|min:1',
            'start_time' => 'nullable|string',
            'price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'is_featured' => 'boolean',
            'status' => 'required|in:upcoming,ongoing,completed,cancelled'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $event->update([
            'title' => $request->title,
            'title_ar' => $request->title_ar,
            'slug' => Str::slug($request->title),
            'content' => $request->content,
            'content_ar' => $request->content_ar,
            'venue_id' => $request->venue_id,
            'event_type' => $request->event_type,
            'organizer' => $request->organizer,
            'duration' => $request->duration,
            'start_time' => $request->start_time,
            'price' => $request->price,
            'sale_price' => $request->sale_price,
            'is_featured' => $request->boolean('is_featured'),
            'status' => $request->status,
            'update_user' => auth()->id()
        ]);

        return redirect()->route('admin.events.index')
                        ->with('success', 'تم تحديث الفعالية بنجاح');
    }

    /**
     * Remove the specified event
     */
    public function destroy(Event $event)
    {
        // Check if event has bookings
        if ($event->ticketBookings()->exists()) {
            return redirect()->back()
                           ->with('error', 'لا يمكن حذف الفعالية لوجود حجوزات عليها');
        }

        $event->delete();

        return redirect()->route('admin.events.index')
                        ->with('success', 'تم حذف الفعالية بنجاح');
    }

    /**
     * Manage event ticket categories
     */
    public function tickets(Event $event)
    {
        $ticketCategories = $event->ticketCategories()->get();
        
        return view('admin.events.tickets', compact('event', 'ticketCategories'));
    }

    /**
     * Store ticket category
     */
    public function storeTicketCategory(Request $request, Event $event)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'name_ar' => 'nullable|string|max:100',
            'price' => 'required|numeric|min:0',
            'total_quantity' => 'required|integer|min:1',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'section' => 'nullable|string|max:50',
            'row_range' => 'nullable|string|max:50',
            'is_vip' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        TicketCategory::create([
            'event_id' => $event->id,
            'name' => $request->name,
            'name_ar' => $request->name_ar,
            'price' => $request->price,
            'total_quantity' => $request->total_quantity,
            'available_quantity' => $request->total_quantity,
            'description' => $request->description,
            'description_ar' => $request->description_ar,
            'section' => $request->section,
            'row_range' => $request->row_range,
            'is_vip' => $request->boolean('is_vip'),
            'create_user' => auth()->id()
        ]);

        return redirect()->back()
                        ->with('success', 'تم إضافة فئة التذاكر بنجاح');
    }

    /**
     * Generate individual tickets for all categories
     */
    public function generateTickets(Event $event)
    {
        try {
            $ticketCategories = $event->ticketCategories;
            $generatedCount = 0;

            foreach ($ticketCategories as $category) {
                // Delete existing tickets for this category
                $category->individualTickets()->delete();

                // Generate new tickets
                for ($i = 1; $i <= $category->quantity; $i++) {
                    IndividualTicket::create([
                        'ticket_category_id' => $category->id,
                        'ticket_number' => $this->generateTicketNumber($event, $category, $i),
                        'qr_code' => $this->generateQRCode($event, $category, $i),
                        'price' => $category->price,
                        'status' => 'available',
                        'create_user' => auth()->id(),
                    ]);
                    $generatedCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "تم إنشاء {$generatedCount} تذكرة بنجاح"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء التذاكر: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Duplicate an event
     */
    public function duplicate(Event $event)
    {
        try {
            $newEvent = $event->replicate();
            $newEvent->title = $event->title . ' (نسخة)';
            $newEvent->status = 'upcoming';
            $newEvent->create_user = auth()->id();
            $newEvent->save();

            // Copy event dates
            foreach ($event->dates as $date) {
                $newDate = $date->replicate();
                $newDate->event_id = $newEvent->id;
                $newDate->save();
            }

            // Copy ticket categories
            foreach ($event->ticketCategories as $category) {
                $newCategory = $category->replicate();
                $newCategory->event_id = $newEvent->id;
                $newCategory->save();
            }

            return response()->json([
                'success' => true,
                'message' => 'تم نسخ الفعالية بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء النسخ: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate ticket number
     */
    private function generateTicketNumber($event, $category, $index)
    {
        return 'TK' . $event->id . $category->id . str_pad($index, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generate QR code
     */
    private function generateQRCode($event, $category, $index)
    {
        return 'QR' . $event->id . $category->id . $index . time();
    }
}
