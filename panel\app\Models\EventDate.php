<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EventDate extends Model
{
    use HasFactory;

    protected $fillable = [
        'target_id',
        'start_date',
        'end_date',
        'ticket_types',
        'active',
        'note_to_customer',
        'note_to_admin',
        'is_instant',
        'create_user',
        'update_user'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'ticket_types' => 'array',
        'active' => 'boolean',
        'is_instant' => 'boolean'
    ];

    public function event()
    {
        return $this->belongsTo(Event::class, 'target_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
