import 'package:flutter/material.dart';
import 'package:travel_offers/data/model/event/event_model.dart';

class EventDetailsScreen extends StatefulWidget {
  final EventModel event;

  const EventDetailsScreen({
    Key? key,
    required this.event,
  }) : super(key: key);

  @override
  State<EventDetailsScreen> createState() => _EventDetailsScreenState();
}

class _EventDetailsScreenState extends State<EventDetailsScreen> {
  TicketCategoryModel? _selectedCategory;
  int _ticketQuantity = 1;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل الفعالية'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event Image
            _buildEventImage(),
            
            // Event Info
            _buildEventInfo(),
            
            // Venue Info
            _buildVenueInfo(),
            
            // Ticket Categories
            _buildTicketCategories(),
            
            // Event Description
            _buildEventDescription(),
          ],
        ),
      ),
      bottomNavigationBar: _buildBookingBar(),
    );
  }

  Widget _buildEventImage() {
    return Container(
      height: 250,
      width: double.infinity,
      decoration: BoxDecoration(
        image: widget.event.imageUrl != null
            ? DecorationImage(
                image: NetworkImage(widget.event.imageUrl!),
                fit: BoxFit.cover,
              )
            : null,
        color: widget.event.imageUrl == null ? Colors.grey[300] : null,
      ),
      child: widget.event.imageUrl == null
          ? Center(
              child: Icon(
                Icons.event,
                size: 80,
                color: Colors.grey[600],
              ),
            )
          : Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.event.isFeatured)
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'مميز',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      SizedBox(height: 8),
                      Text(
                        widget.event.titleAr,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildEventInfo() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.calendar_today, color: Theme.of(context).primaryColor),
              SizedBox(width: 8),
              Text(
                'معلومات الفعالية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'النوع',
                  _getEventTypeArabic(widget.event.eventType),
                  Icons.category,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'المدة',
                  '${widget.event.duration} دقيقة',
                  Icons.access_time,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'المنظم',
                  widget.event.organizer,
                  Icons.business,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'التقييم',
                  '${widget.event.reviewScore}/5',
                  Icons.star,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: Colors.grey[600]),
            SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildVenueInfo() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: Theme.of(context).primaryColor),
              SizedBox(width: 8),
              Text(
                'معلومات المكان',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          
          Text(
            widget.event.venue.nameAr,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          SizedBox(height: 4),
          
          Text(
            '${widget.event.venue.city}, ${widget.event.venue.country}',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          SizedBox(height: 8),
          
          Row(
            children: [
              Icon(Icons.people, size: 16, color: Colors.grey[600]),
              SizedBox(width: 4),
              Text(
                'السعة: ${widget.event.venue.capacity.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')} شخص',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTicketCategories() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.confirmation_number, color: Theme.of(context).primaryColor),
              SizedBox(width: 8),
              Text(
                'فئات التذاكر',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          
          ...widget.event.ticketCategories.map((category) => 
            _buildTicketCategoryCard(category)
          ).toList(),
        ],
      ),
    );
  }

  Widget _buildTicketCategoryCard(TicketCategoryModel category) {
    final isSelected = _selectedCategory?.id == category.id;
    final isAvailable = category.availableQuantity > 0;
    
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: isAvailable ? () {
          setState(() {
            _selectedCategory = isSelected ? null : category;
            _ticketQuantity = 1;
          });
        } : null,
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected ? Theme.of(context).primaryColor.withValues(alpha: 0.1) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected 
                  ? Theme.of(context).primaryColor 
                  : isAvailable 
                      ? Colors.grey[300]! 
                      : Colors.red[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              category.nameAr,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isAvailable ? Colors.black : Colors.grey,
                              ),
                            ),
                            if (category.isVip) ...[
                              SizedBox(width: 8),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.amber,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  'VIP',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        SizedBox(height: 4),
                        Text(
                          isAvailable 
                              ? 'متاح: ${category.availableQuantity} تذكرة'
                              : 'نفدت التذاكر',
                          style: TextStyle(
                            color: isAvailable ? Colors.green : Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${category.price.toStringAsFixed(3)} د.ك',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isAvailable 
                              ? Theme.of(context).primaryColor 
                              : Colors.grey,
                        ),
                      ),
                      if (isSelected)
                        Icon(
                          Icons.check_circle,
                          color: Theme.of(context).primaryColor,
                        ),
                    ],
                  ),
                ],
              ),
              
              if (isSelected) ...[
                SizedBox(height: 16),
                Row(
                  children: [
                    Text('الكمية: ', style: TextStyle(fontWeight: FontWeight.bold)),
                    SizedBox(width: 8),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: _ticketQuantity > 1 ? () {
                              setState(() {
                                _ticketQuantity--;
                              });
                            } : null,
                            icon: Icon(Icons.remove),
                            iconSize: 16,
                          ),
                          Text(
                            _ticketQuantity.toString(),
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          IconButton(
                            onPressed: _ticketQuantity < category.availableQuantity ? () {
                              setState(() {
                                _ticketQuantity++;
                              });
                            } : null,
                            icon: Icon(Icons.add),
                            iconSize: 16,
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    Text(
                      'المجموع: ${(_ticketQuantity * category.price).toStringAsFixed(3)} د.ك',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEventDescription() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.description, color: Theme.of(context).primaryColor),
              SizedBox(width: 8),
              Text(
                'وصف الفعالية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Text(
            widget.event.contentAr,
            style: TextStyle(
              fontSize: 14,
              height: 1.6,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingBar() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            if (_selectedCategory != null) ...[
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedCategory!.nameAr,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      '${_ticketQuantity} × ${_selectedCategory!.price.toStringAsFixed(3)} د.ك',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16),
            ],
            Expanded(
              flex: _selectedCategory != null ? 2 : 1,
              child: ElevatedButton(
                onPressed: _selectedCategory != null ? _bookTickets : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  _selectedCategory != null 
                      ? 'احجز الآن - ${(_ticketQuantity * _selectedCategory!.price).toStringAsFixed(3)} د.ك'
                      : 'اختر فئة التذكرة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getEventTypeArabic(String eventType) {
    switch (eventType) {
      case 'football':
        return 'كرة القدم';
      case 'basketball':
        return 'كرة السلة';
      case 'concert':
        return 'حفلة موسيقية';
      case 'theater':
        return 'مسرحية';
      case 'conference':
        return 'مؤتمر';
      default:
        return 'فعالية';
    }
  }

  void _bookTickets() {
    if (_selectedCategory == null) return;
    
    // Navigate to booking screen
    Navigator.pushNamed(
      context,
      '/event-booking',
      arguments: {
        'event': widget.event,
        'category': _selectedCategory,
        'quantity': _ticketQuantity,
      },
    );
  }
}
