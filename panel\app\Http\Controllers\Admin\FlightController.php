<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Flight;
use App\Models\Airline;
use App\Models\Airport;
use App\Models\FlightSeat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FlightController extends Controller
{
    /**
     * Display a listing of flights
     */
    public function index(Request $request)
    {
        $query = Flight::with(['airline', 'departureAirport', 'arrivalAirport']);

        // Search filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('flight_number', 'like', "%{$search}%");
            });
        }

        if ($request->filled('airline_id')) {
            $query->where('airline_id', $request->airline_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('departure_date')) {
            $query->whereDate('departure_time', $request->departure_date);
        }

        $flights = $query->orderBy('departure_time', 'desc')->paginate(20);
        $airlines = Airline::active()->get();

        return view('admin.flights.index', compact('flights', 'airlines'));
    }

    /**
     * Show the form for creating a new flight
     */
    public function create()
    {
        $airlines = Airline::active()->get();
        $airports = Airport::active()->get();
        
        return view('admin.flights.create', compact('airlines', 'airports'));
    }

    /**
     * Store a newly created flight
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'flight_number' => 'required|string|max:20',
            'airline_id' => 'required|exists:airlines,id',
            'airport_from' => 'required|exists:airports,id',
            'airport_to' => 'required|exists:airports,id|different:airport_from',
            'departure_time' => 'required|date|after:now',
            'arrival_time' => 'required|date|after:departure_time',
            'aircraft_type' => 'nullable|string|max:100',
            'min_price' => 'required|numeric|min:0',
            'status' => 'required|in:scheduled,delayed,cancelled'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        // Calculate duration
        $departureTime = new \DateTime($request->departure_time);
        $arrivalTime = new \DateTime($request->arrival_time);
        $duration = $arrivalTime->diff($departureTime)->h * 60 + $arrivalTime->diff($departureTime)->i;

        $flight = Flight::create([
            'title' => $request->title,
            'code' => $this->generateFlightCode(),
            'flight_number' => $request->flight_number,
            'airline_id' => $request->airline_id,
            'airport_from' => $request->airport_from,
            'airport_to' => $request->airport_to,
            'departure_time' => $request->departure_time,
            'arrival_time' => $request->arrival_time,
            'duration' => $duration,
            'aircraft_type' => $request->aircraft_type,
            'min_price' => $request->min_price,
            'status' => $request->status,
            'create_user' => auth()->id()
        ]);

        return redirect()->route('admin.flights.index')
                        ->with('success', 'تم إنشاء الرحلة بنجاح');
    }

    /**
     * Display the specified flight
     */
    public function show(Flight $flight)
    {
        $flight->load(['airline', 'departureAirport', 'arrivalAirport', 'seats', 'bookings']);
        
        return view('admin.flights.show', compact('flight'));
    }

    /**
     * Show the form for editing the specified flight
     */
    public function edit(Flight $flight)
    {
        $airlines = Airline::active()->get();
        $airports = Airport::active()->get();
        
        return view('admin.flights.edit', compact('flight', 'airlines', 'airports'));
    }

    /**
     * Update the specified flight
     */
    public function update(Request $request, Flight $flight)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'flight_number' => 'required|string|max:20',
            'airline_id' => 'required|exists:airlines,id',
            'airport_from' => 'required|exists:airports,id',
            'airport_to' => 'required|exists:airports,id|different:airport_from',
            'departure_time' => 'required|date',
            'arrival_time' => 'required|date|after:departure_time',
            'aircraft_type' => 'nullable|string|max:100',
            'min_price' => 'required|numeric|min:0',
            'status' => 'required|in:scheduled,delayed,cancelled,completed'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        // Calculate duration
        $departureTime = new \DateTime($request->departure_time);
        $arrivalTime = new \DateTime($request->arrival_time);
        $duration = $arrivalTime->diff($departureTime)->h * 60 + $arrivalTime->diff($departureTime)->i;

        $flight->update([
            'title' => $request->title,
            'flight_number' => $request->flight_number,
            'airline_id' => $request->airline_id,
            'airport_from' => $request->airport_from,
            'airport_to' => $request->airport_to,
            'departure_time' => $request->departure_time,
            'arrival_time' => $request->arrival_time,
            'duration' => $duration,
            'aircraft_type' => $request->aircraft_type,
            'min_price' => $request->min_price,
            'status' => $request->status,
            'update_user' => auth()->id()
        ]);

        return redirect()->route('admin.flights.index')
                        ->with('success', 'تم تحديث الرحلة بنجاح');
    }

    /**
     * Remove the specified flight
     */
    public function destroy(Flight $flight)
    {
        // Check if flight has bookings
        if ($flight->bookings()->exists()) {
            return redirect()->back()
                           ->with('error', 'لا يمكن حذف الرحلة لوجود حجوزات عليها');
        }

        $flight->delete();

        return redirect()->route('admin.flights.index')
                        ->with('success', 'تم حذف الرحلة بنجاح');
    }

    /**
     * Manage flight seats
     */
    public function seats(Flight $flight)
    {
        $seats = $flight->seats()->with('seatType')->get();
        
        return view('admin.flights.seats', compact('flight', 'seats'));
    }

    /**
     * Generate unique flight code
     */
    private function generateFlightCode()
    {
        do {
            $code = 'FL' . strtoupper(substr(uniqid(), -6));
        } while (Flight::where('code', $code)->exists());

        return $code;
    }
}
