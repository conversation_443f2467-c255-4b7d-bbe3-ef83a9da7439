import 'package:flutter/material.dart';
import 'package:travel_offers/data/model/kuwait/kuwait_area_model.dart';
import 'package:travel_offers/data/services/kuwait_weather_service.dart';
import 'package:travel_offers/data/services/kuwait_holidays_service.dart';

class KuwaitInfoScreen extends StatefulWidget {
  const KuwaitInfoScreen({Key? key}) : super(key: key);

  @override
  State<KuwaitInfoScreen> createState() => _KuwaitInfoScreenState();
}

class _KuwaitInfoScreenState extends State<KuwaitInfoScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  KuwaitWeatherModel? _weather;
  List<KuwaitHolidayModel> _upcomingHolidays = [];
  PrayerTimesModel? _prayerTimes;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final weather = await KuwaitWeatherService.getCurrentWeather();
      final prayerTimes = await KuwaitWeatherService.getPrayerTimes();
      final holidays = KuwaitHolidaysService.getUpcomingHolidays(limit: 5);

      setState(() {
        _weather = weather;
        _prayerTimes = prayerTimes;
        _upcomingHolidays = holidays;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('معلومات الكويت'),
        backgroundColor: Theme.of(context).primaryColor,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(icon: Icon(Icons.wb_sunny), text: 'الطقس'),
            Tab(icon: Icon(Icons.access_time), text: 'الصلاة'),
            Tab(icon: Icon(Icons.event), text: 'المناسبات'),
            Tab(icon: Icon(Icons.location_city), text: 'المناطق'),
            Tab(icon: Icon(Icons.phone), text: 'أرقام مهمة'),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildWeatherTab(),
                _buildPrayerTimesTab(),
                _buildHolidaysTab(),
                _buildAreasTab(),
                _buildEmergencyTab(),
              ],
            ),
    );
  }

  Widget _buildWeatherTab() {
    if (_weather == null) {
      return Center(child: Text('لا توجد بيانات طقس متاحة'));
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current Weather Card
          Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الكويت',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            _weather!.conditionAr,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            _weather!.temperatureDisplay,
                            style: TextStyle(
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          Text(
                            'يشعر بـ ${_weather!.feelsLikeDisplay}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildWeatherDetail(
                        'الرطوبة',
                        _weather!.humidityDisplay,
                        Icons.water_drop,
                      ),
                      _buildWeatherDetail(
                        'الرياح',
                        _weather!.windSpeedDisplay,
                        Icons.air,
                      ),
                      _buildWeatherDetail(
                        'الرؤية',
                        _weather!.visibilityDisplay,
                        Icons.visibility,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 16),

          // Weather Warnings
          if (_weather!.heatWarningAr.isNotEmpty) ...[
            Card(
              color: Colors.orange[50],
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _weather!.heatWarningAr,
                        style: TextStyle(
                          color: Colors.orange[800],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
          ],

          if (_weather!.dustWarningAr.isNotEmpty) ...[
            Card(
              color: Colors.brown[50],
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.brown),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _weather!.dustWarningAr,
                        style: TextStyle(
                          color: Colors.brown[800],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
          ],

          // Weather Tips
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'نصائح الطقس',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 12),
                  ...KuwaitWeatherService.getWeatherTips(_weather!)
                      .map((tip) => Padding(
                            padding: EdgeInsets.only(bottom: 8),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.lightbulb,
                                  size: 16,
                                  color: Colors.amber,
                                ),
                                SizedBox(width: 8),
                                Expanded(child: Text(tip)),
                              ],
                            ),
                          ))
                      .toList(),
                ],
              ),
            ),
          ),

          SizedBox(height: 16),

          // 5-Day Forecast
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توقعات الأيام القادمة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 12),
                  ...(_weather!.forecast.take(5).map((forecast) => 
                    Padding(
                      padding: EdgeInsets.only(bottom: 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            forecast.dayName,
                            style: TextStyle(fontWeight: FontWeight.w500),
                          ),
                          Text(forecast.conditionAr),
                          Row(
                            children: [
                              Text(
                                forecast.maxTempDisplay,
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Text(' / '),
                              Text(
                                forecast.minTempDisplay,
                                style: TextStyle(color: Colors.grey[600]),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  )).toList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherDetail(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildPrayerTimesTab() {
    if (_prayerTimes == null) {
      return Center(child: Text('لا توجد بيانات مواقيت الصلاة'));
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          // Next Prayer Card
          if (_prayerTimes!.nextPrayer != null) ...[
            Card(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [
                    Text(
                      'الصلاة القادمة',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      _prayerTimes!.nextPrayerNameAr,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'خلال ${_prayerTimes!.timeToNextPrayerDisplay}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
          ],

          // Prayer Times List
          Card(
            child: Column(
              children: [
                _buildPrayerTimeItem('الفجر', _prayerTimes!.fajrTime, Icons.brightness_2),
                Divider(height: 1),
                _buildPrayerTimeItem('الشروق', _prayerTimes!.sunriseTime, Icons.wb_sunny),
                Divider(height: 1),
                _buildPrayerTimeItem('الظهر', _prayerTimes!.dhuhrTime, Icons.wb_sunny),
                Divider(height: 1),
                _buildPrayerTimeItem('العصر', _prayerTimes!.asrTime, Icons.brightness_6),
                Divider(height: 1),
                _buildPrayerTimeItem('المغرب', _prayerTimes!.maghribTime, Icons.brightness_3),
                Divider(height: 1),
                _buildPrayerTimeItem('العشاء', _prayerTimes!.ishaTime, Icons.brightness_2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrayerTimeItem(String name, String time, IconData icon) {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(icon, color: Theme.of(context).primaryColor),
          SizedBox(width: 16),
          Expanded(
            child: Text(
              name,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            time,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHolidaysTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المناسبات القادمة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          ..._upcomingHolidays.map((holiday) => Card(
                margin: EdgeInsets.only(bottom: 12),
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              holiday.displayName,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              holiday.daysUntilDisplay,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        holiday.displayDescription,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      if (holiday.traditions.isNotEmpty) ...[
                        SizedBox(height: 12),
                        Text(
                          'التقاليد:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        SizedBox(height: 4),
                        Wrap(
                          spacing: 8,
                          runSpacing: 4,
                          children: holiday.traditions.take(3).map((tradition) =>
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                tradition,
                                style: TextStyle(fontSize: 12),
                              ),
                            ),
                          ).toList(),
                        ),
                      ],
                    ],
                  ),
                ),
              )).toList(),
        ],
      ),
    );
  }

  Widget _buildAreasTab() {
    final governorates = KuwaitAreasService.getKuwaitGovernorates();
    
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: governorates.length,
      itemBuilder: (context, index) {
        final governorate = governorates[index];
        return Card(
          margin: EdgeInsets.only(bottom: 12),
          child: ExpansionTile(
            title: Text(
              governorate.displayName,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            subtitle: Text('${governorate.areas.length} منطقة'),
            children: governorate.areas.map((area) => ListTile(
              title: Text(area.displayName),
              subtitle: area.landmarks.isNotEmpty 
                  ? Text(area.landmarks.take(2).join(', '))
                  : null,
              trailing: area.isPopular 
                  ? Icon(Icons.star, color: Colors.amber)
                  : null,
              onTap: () {
                // Show area details
                _showAreaDetails(area);
              },
            )).toList(),
          ),
        );
      },
    );
  }

  Widget _buildEmergencyTab() {
    final emergencyNumbers = [
      {'name': 'الطوارئ العامة', 'number': '112', 'icon': Icons.emergency},
      {'name': 'الشرطة', 'number': '112', 'icon': Icons.local_police},
      {'name': 'الإسعاف', 'number': '112', 'icon': Icons.local_hospital},
      {'name': 'الإطفاء', 'number': '112', 'icon': Icons.local_fire_department},
      {'name': 'شرطة المرور', 'number': '112', 'icon': Icons.traffic},
    ];

    final usefulNumbers = [
      {'name': 'مطار الكويت الدولي', 'number': '********', 'icon': Icons.flight},
      {'name': 'الإدارة العامة للطيران المدني', 'number': '********', 'icon': Icons.airplanemode_active},
      {'name': 'وزارة الصحة', 'number': '********', 'icon': Icons.local_hospital},
      {'name': 'وزارة الداخلية', 'number': '********', 'icon': Icons.account_balance},
      {'name': 'الكهرباء والماء', 'number': '152', 'icon': Icons.electrical_services},
    ];

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أرقام الطوارئ',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          SizedBox(height: 16),
          ...emergencyNumbers.map((item) => Card(
                color: Colors.red[50],
                margin: EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Icon(
                    item['icon'] as IconData,
                    color: Colors.red,
                  ),
                  title: Text(
                    item['name'] as String,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  trailing: Text(
                    item['number'] as String,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                  onTap: () {
                    // Call number functionality
                  },
                ),
              )).toList(),
          
          SizedBox(height: 24),
          
          Text(
            'أرقام مفيدة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          ...usefulNumbers.map((item) => Card(
                margin: EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Icon(
                    item['icon'] as IconData,
                    color: Theme.of(context).primaryColor,
                  ),
                  title: Text(
                    item['name'] as String,
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  trailing: Text(
                    item['number'] as String,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  onTap: () {
                    // Call number functionality
                  },
                ),
              )).toList(),
        ],
      ),
    );
  }

  void _showAreaDetails(KuwaitAreaModel area) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              area.displayName,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            if (area.landmarks.isNotEmpty) ...[
              Text(
                'المعالم المهمة:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              ...area.landmarks.map((landmark) => Padding(
                    padding: EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, size: 16),
                        SizedBox(width: 8),
                        Text(landmark),
                      ],
                    ),
                  )).toList(),
            ],
          ],
        ),
      ),
    );
  }
}
