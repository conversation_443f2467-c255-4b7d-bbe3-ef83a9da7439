@extends('layouts.admin')

@section('title', 'إدارة المباريات والفعاليات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-alt"></i>
                        المباريات والفعاليات
                    </h3>
                    <div>
                        <a href="{{ route('admin.events.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة فعالية جديدة
                        </a>
                        <button class="btn btn-info" data-toggle="modal" data-target="#importModal">
                            <i class="fas fa-upload"></i>
                            استيراد فعاليات
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-calendar-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي الفعاليات</span>
                                    <span class="info-box-number">{{ $totalEvents }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-play"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">نشطة</span>
                                    <span class="info-box-number">{{ $activeEvents }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-ticket-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">التذاكر المباعة</span>
                                    <span class="info-box-number">{{ $soldTickets }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-dollar-sign"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي الإيرادات</span>
                                    <span class="info-box-number">{{ number_format($totalRevenue, 3) }} د.ك</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-2">
                            <input type="text" class="form-control" id="event-name-filter" 
                                   placeholder="اسم الفعالية">
                        </div>
                        <div class="col-md-2">
                            <select class="form-control" id="venue-filter">
                                <option value="">جميع الأماكن</option>
                                @foreach($venues as $venue)
                                    <option value="{{ $venue->id }}">{{ $venue->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-control" id="category-filter">
                                <option value="">جميع الفئات</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-control" id="status-filter">
                                <option value="">جميع الحالات</option>
                                <option value="upcoming">قادمة</option>
                                <option value="ongoing">جارية</option>
                                <option value="completed">مكتملة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" id="date-filter" placeholder="التاريخ">
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary btn-block" onclick="filterEvents()">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                        </div>
                    </div>

                    <!-- جدول الفعاليات -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="events-table">
                            <thead>
                                <tr>
                                    <th>الفعالية</th>
                                    <th>المكان</th>
                                    <th>التاريخ والوقت</th>
                                    <th>الفئة</th>
                                    <th>التذاكر</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($events as $event)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($event->image)
                                                <img src="{{ $event->image }}" alt="{{ $event->title }}" 
                                                     class="img-thumbnail me-2" style="width: 50px; height: 50px;">
                                            @endif
                                            <div>
                                                <strong>{{ $event->title }}</strong>
                                                @if($event->is_featured)
                                                    <span class="badge badge-warning">مميز</span>
                                                @endif
                                                <div>
                                                    <small class="text-muted">{{ Str::limit($event->description, 50) }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $event->venue->name }}</strong>
                                        </div>
                                        <small class="text-muted">
                                            {{ $event->venue->city }}, {{ $event->venue->country }}
                                        </small>
                                        <div>
                                            <small class="text-muted">
                                                السعة: {{ number_format($event->venue->capacity) }}
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        @foreach($event->dates as $date)
                                            <div>
                                                <strong>{{ $date->event_date->format('Y-m-d') }}</strong>
                                            </div>
                                            <small class="text-muted">{{ $date->start_time }} - {{ $date->end_time }}</small>
                                            @if(!$loop->last)<hr class="my-1">@endif
                                        @endforeach
                                    </td>
                                    <td>
                                        @foreach($event->terms as $term)
                                            <span class="badge badge-secondary">{{ $term->name }}</span>
                                        @endforeach
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $event->total_tickets }}</strong> إجمالي
                                        </div>
                                        <div>
                                            <span class="text-success">{{ $event->sold_tickets }}</span> مباع
                                        </div>
                                        <div>
                                            <span class="text-info">{{ $event->available_tickets }}</span> متاح
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            من <strong>{{ number_format($event->min_price, 3) }}</strong> د.ك
                                        </div>
                                        <div>
                                            إلى <strong>{{ number_format($event->max_price, 3) }}</strong> د.ك
                                        </div>
                                    </td>
                                    <td>
                                        @switch($event->status)
                                            @case('upcoming')
                                                <span class="badge badge-primary">قادمة</span>
                                                @break
                                            @case('ongoing')
                                                <span class="badge badge-success">جارية</span>
                                                @break
                                            @case('completed')
                                                <span class="badge badge-info">مكتملة</span>
                                                @break
                                            @case('cancelled')
                                                <span class="badge badge-danger">ملغية</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.events.show', $event->id) }}" 
                                               class="btn btn-sm btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.events.edit', $event->id) }}" 
                                               class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('admin.events.tickets', $event->id) }}" 
                                               class="btn btn-sm btn-success" title="إدارة التذاكر">
                                                <i class="fas fa-ticket-alt"></i>
                                            </a>
                                            <button class="btn btn-sm btn-secondary" 
                                                    onclick="duplicateEvent({{ $event->id }})" title="نسخ">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" 
                                                    onclick="deleteEvent({{ $event->id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $events->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد فعاليات من ملف Excel</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.events.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>ملف Excel</label>
                        <input type="file" name="file" class="form-control" accept=".xlsx,.xls" required>
                        <small class="form-text text-muted">
                            يجب أن يحتوي الملف على الأعمدة: title, description, venue_id, event_date, start_time, end_time
                        </small>
                    </div>
                    <div class="form-group">
                        <a href="{{ route('admin.events.template') }}" class="btn btn-link">
                            <i class="fas fa-download"></i>
                            تحميل قالب Excel
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">استيراد</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#events-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 2, "asc" ]],
        "pageLength": 25
    });
});

function filterEvents() {
    const eventName = $('#event-name-filter').val();
    const venue = $('#venue-filter').val();
    const category = $('#category-filter').val();
    const status = $('#status-filter').val();
    const date = $('#date-filter').val();
    
    const params = new URLSearchParams();
    if (eventName) params.append('event_name', eventName);
    if (venue) params.append('venue', venue);
    if (category) params.append('category', category);
    if (status) params.append('status', status);
    if (date) params.append('date', date);
    
    window.location.href = '{{ route("admin.events.index") }}?' + params.toString();
}

function duplicateEvent(id) {
    if (confirm('هل تريد نسخ هذه الفعالية؟')) {
        $.ajax({
            url: '/admin/events/' + id + '/duplicate',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء النسخ');
                }
            }
        });
    }
}

function deleteEvent(id) {
    if (confirm('هل أنت متأكد من حذف هذه الفعالية؟ سيتم حذف جميع التذاكر المرتبطة بها.')) {
        $.ajax({
            url: '/admin/events/' + id,
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء الحذف');
                }
            }
        });
    }
}
</script>
@endsection
