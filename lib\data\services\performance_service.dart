import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:travel_offers/data/services/cache_service.dart';

class PerformanceMetrics {
  final String operation;
  final Duration duration;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;
  final bool isSuccess;
  final String? errorMessage;

  PerformanceMetrics({
    required this.operation,
    required this.duration,
    required this.timestamp,
    this.metadata,
    required this.isSuccess,
    this.errorMessage,
  });

  Map<String, dynamic> toJson() {
    return {
      'operation': operation,
      'duration': duration.inMilliseconds,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
      'isSuccess': isSuccess,
      'errorMessage': errorMessage,
    };
  }

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) {
    return PerformanceMetrics(
      operation: json['operation'],
      duration: Duration(milliseconds: json['duration']),
      timestamp: DateTime.parse(json['timestamp']),
      metadata: json['metadata'],
      isSuccess: json['isSuccess'],
      errorMessage: json['errorMessage'],
    );
  }
}

class PerformanceService {
  static const String _metricsKey = 'performance_metrics';
  static const int _maxStoredMetrics = 1000;
  static final Map<String, Stopwatch> _activeOperations = {};

  // Start tracking an operation
  static void startOperation(String operationName) {
    _activeOperations[operationName] = Stopwatch()..start();
  }

  // End tracking an operation
  static void endOperation(
    String operationName, {
    bool isSuccess = true,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    final stopwatch = _activeOperations.remove(operationName);
    if (stopwatch == null) return;

    stopwatch.stop();
    
    final metrics = PerformanceMetrics(
      operation: operationName,
      duration: stopwatch.elapsed,
      timestamp: DateTime.now(),
      metadata: metadata,
      isSuccess: isSuccess,
      errorMessage: errorMessage,
    );

    _storeMetrics(metrics);
    
    if (kDebugMode) {
      final status = isSuccess ? '✅' : '❌';
      print('$status Performance: $operationName took ${stopwatch.elapsedMilliseconds}ms');
    }
  }

  // Track a future operation
  static Future<T> trackOperation<T>(
    String operationName,
    Future<T> Function() operation, {
    Map<String, dynamic>? metadata,
  }) async {
    startOperation(operationName);
    
    try {
      final result = await operation();
      endOperation(operationName, isSuccess: true, metadata: metadata);
      return result;
    } catch (e) {
      endOperation(
        operationName,
        isSuccess: false,
        errorMessage: e.toString(),
        metadata: metadata,
      );
      rethrow;
    }
  }

  // Track a synchronous operation
  static T trackSync<T>(
    String operationName,
    T Function() operation, {
    Map<String, dynamic>? metadata,
  }) {
    startOperation(operationName);
    
    try {
      final result = operation();
      endOperation(operationName, isSuccess: true, metadata: metadata);
      return result;
    } catch (e) {
      endOperation(
        operationName,
        isSuccess: false,
        errorMessage: e.toString(),
        metadata: metadata,
      );
      rethrow;
    }
  }

  // Get stored metrics
  static List<PerformanceMetrics> getStoredMetrics() {
    try {
      final metricsString = CacheService.prefs.getString(_metricsKey);
      if (metricsString == null) return [];
      
      final metricsList = json.decode(metricsString) as List<dynamic>;
      return metricsList
          .map((json) => PerformanceMetrics.fromJson(json))
          .toList()
          ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    } catch (e) {
      return [];
    }
  }

  // Get performance statistics
  static Map<String, dynamic> getPerformanceStatistics() {
    final metrics = getStoredMetrics();
    if (metrics.isEmpty) return {};

    final now = DateTime.now();
    final last24Hours = metrics.where((m) => 
        now.difference(m.timestamp).inHours <= 24).toList();
    
    final operationStats = <String, Map<String, dynamic>>{};
    
    for (final metric in last24Hours) {
      if (!operationStats.containsKey(metric.operation)) {
        operationStats[metric.operation] = {
          'count': 0,
          'totalDuration': Duration.zero,
          'successCount': 0,
          'failureCount': 0,
          'minDuration': metric.duration,
          'maxDuration': metric.duration,
        };
      }
      
      final stats = operationStats[metric.operation]!;
      stats['count'] = stats['count'] + 1;
      stats['totalDuration'] = stats['totalDuration'] + metric.duration;
      
      if (metric.isSuccess) {
        stats['successCount'] = stats['successCount'] + 1;
      } else {
        stats['failureCount'] = stats['failureCount'] + 1;
      }
      
      if (metric.duration < stats['minDuration']) {
        stats['minDuration'] = metric.duration;
      }
      if (metric.duration > stats['maxDuration']) {
        stats['maxDuration'] = metric.duration;
      }
    }

    // Calculate averages and success rates
    for (final stats in operationStats.values) {
      final count = stats['count'] as int;
      final totalDuration = stats['totalDuration'] as Duration;
      final successCount = stats['successCount'] as int;
      
      stats['averageDuration'] = Duration(
        milliseconds: totalDuration.inMilliseconds ~/ count,
      );
      stats['successRate'] = count > 0 ? (successCount / count) * 100 : 0.0;
    }

    return {
      'totalOperations': last24Hours.length,
      'successfulOperations': last24Hours.where((m) => m.isSuccess).length,
      'failedOperations': last24Hours.where((m) => !m.isSuccess).length,
      'averageResponseTime': _calculateAverageResponseTime(last24Hours),
      'operationStats': operationStats,
      'slowestOperations': _getSlowestOperations(last24Hours),
      'mostFrequentOperations': _getMostFrequentOperations(last24Hours),
    };
  }

  // Get performance recommendations
  static List<String> getPerformanceRecommendations() {
    final stats = getPerformanceStatistics();
    final recommendations = <String>[];

    if (stats.isEmpty) return recommendations;

    final operationStats = stats['operationStats'] as Map<String, dynamic>? ?? {};
    
    for (final entry in operationStats.entries) {
      final operationName = entry.key;
      final operationData = entry.value as Map<String, dynamic>;
      final averageDuration = operationData['averageDuration'] as Duration;
      final successRate = operationData['successRate'] as double;

      // Check for slow operations
      if (averageDuration.inMilliseconds > 3000) {
        recommendations.add(
          'عملية "$operationName" بطيئة (${averageDuration.inMilliseconds}ms). '
          'فكر في تحسين الأداء أو إضافة تخزين مؤقت.'
        );
      }

      // Check for low success rates
      if (successRate < 90) {
        recommendations.add(
          'عملية "$operationName" لديها معدل نجاح منخفض (${successRate.toStringAsFixed(1)}%). '
          'تحقق من معالجة الأخطاء والاتصال بالشبكة.'
        );
      }
    }

    // General recommendations
    final totalOperations = stats['totalOperations'] as int;
    final failedOperations = stats['failedOperations'] as int;
    
    if (totalOperations > 0) {
      final failureRate = (failedOperations / totalOperations) * 100;
      
      if (failureRate > 10) {
        recommendations.add(
          'معدل الفشل العام مرتفع (${failureRate.toStringAsFixed(1)}%). '
          'تحسين معالجة الأخطاء والاتصال بالشبكة مطلوب.'
        );
      }
    }

    // Cache recommendations
    final cacheSize = CacheService.getCacheSize();
    if (cacheSize > 10 * 1024 * 1024) { // 10MB
      recommendations.add(
        'حجم التخزين المؤقت كبير (${(cacheSize / 1024 / 1024).toStringAsFixed(1)}MB). '
        'فكر في تنظيف البيانات المنتهية الصلاحية.'
      );
    }

    return recommendations;
  }

  // Memory usage tracking
  static Map<String, dynamic> getMemoryUsage() {
    // This would require platform-specific implementation
    // For now, return cache-related memory info
    final cacheSize = CacheService.getCacheSize();
    final cacheStats = CacheService.getCacheStatistics();
    
    return {
      'cacheSize': cacheSize,
      'cacheEntries': cacheStats.length,
      'validCacheEntries': cacheStats.values
          .where((entry) => entry['isValid'] == true)
          .length,
      'expiredCacheEntries': cacheStats.values
          .where((entry) => entry['isValid'] == false)
          .length,
    };
  }

  // Network performance tracking
  static void trackNetworkRequest({
    required String endpoint,
    required Duration duration,
    required int statusCode,
    required int responseSize,
    String? errorMessage,
  }) {
    final isSuccess = statusCode >= 200 && statusCode < 300;
    
    final metrics = PerformanceMetrics(
      operation: 'network_request',
      duration: duration,
      timestamp: DateTime.now(),
      metadata: {
        'endpoint': endpoint,
        'statusCode': statusCode,
        'responseSize': responseSize,
      },
      isSuccess: isSuccess,
      errorMessage: errorMessage,
    );

    _storeMetrics(metrics);
  }

  // Database operation tracking
  static void trackDatabaseOperation({
    required String operation,
    required Duration duration,
    required bool isSuccess,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    final metrics = PerformanceMetrics(
      operation: 'database_$operation',
      duration: duration,
      timestamp: DateTime.now(),
      metadata: metadata,
      isSuccess: isSuccess,
      errorMessage: errorMessage,
    );

    _storeMetrics(metrics);
  }

  // UI performance tracking
  static void trackUIOperation({
    required String screenName,
    required String operation,
    required Duration duration,
    Map<String, dynamic>? metadata,
  }) {
    final metrics = PerformanceMetrics(
      operation: 'ui_${screenName}_$operation',
      duration: duration,
      timestamp: DateTime.now(),
      metadata: metadata,
      isSuccess: true,
    );

    _storeMetrics(metrics);
  }

  // Clear old metrics
  static Future<void> clearOldMetrics() async {
    final metrics = getStoredMetrics();
    final now = DateTime.now();
    
    // Keep only metrics from last 7 days
    final recentMetrics = metrics.where((m) => 
        now.difference(m.timestamp).inDays <= 7).toList();
    
    await _saveMetrics(recentMetrics);
  }

  // Export metrics for analysis
  static String exportMetrics() {
    final metrics = getStoredMetrics();
    final stats = getPerformanceStatistics();
    
    return json.encode({
      'exportDate': DateTime.now().toIso8601String(),
      'statistics': stats,
      'rawMetrics': metrics.map((m) => m.toJson()).toList(),
    });
  }

  // Private methods
  static void _storeMetrics(PerformanceMetrics metrics) {
    try {
      final storedMetrics = getStoredMetrics();
      storedMetrics.insert(0, metrics);
      
      // Keep only the most recent metrics
      if (storedMetrics.length > _maxStoredMetrics) {
        storedMetrics.removeRange(_maxStoredMetrics, storedMetrics.length);
      }
      
      _saveMetrics(storedMetrics);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to store performance metrics: $e');
      }
    }
  }

  static Future<void> _saveMetrics(List<PerformanceMetrics> metrics) async {
    final metricsJson = metrics.map((m) => m.toJson()).toList();
    await CacheService.prefs.setString(_metricsKey, json.encode(metricsJson));
  }

  static Duration _calculateAverageResponseTime(List<PerformanceMetrics> metrics) {
    if (metrics.isEmpty) return Duration.zero;
    
    final totalMilliseconds = metrics
        .map((m) => m.duration.inMilliseconds)
        .reduce((a, b) => a + b);
    
    return Duration(milliseconds: totalMilliseconds ~/ metrics.length);
  }

  static List<Map<String, dynamic>> _getSlowestOperations(
    List<PerformanceMetrics> metrics,
  ) {
    final sorted = List<PerformanceMetrics>.from(metrics)
        ..sort((a, b) => b.duration.compareTo(a.duration));
    
    return sorted.take(5).map((m) => {
      'operation': m.operation,
      'duration': m.duration.inMilliseconds,
      'timestamp': m.timestamp.toIso8601String(),
    }).toList();
  }

  static List<Map<String, dynamic>> _getMostFrequentOperations(
    List<PerformanceMetrics> metrics,
  ) {
    final operationCounts = <String, int>{};
    
    for (final metric in metrics) {
      operationCounts[metric.operation] = 
          (operationCounts[metric.operation] ?? 0) + 1;
    }
    
    final sorted = operationCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
    
    return sorted.take(5).map((entry) => {
      'operation': entry.key,
      'count': entry.value,
    }).toList();
  }
}
