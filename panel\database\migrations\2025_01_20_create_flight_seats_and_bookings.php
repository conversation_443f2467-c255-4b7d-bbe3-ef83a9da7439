<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول مقاعد الرحلات
        Schema::create('flight_seats', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->decimal('price', 12, 2)->nullable();
            $table->integer('max_passengers')->nullable();
            $table->bigInteger('flight_id')->nullable();
            $table->string('seat_type')->nullable();
            $table->string('seat_number')->nullable();
            $table->string('person')->nullable(); // adult, child, infant
            $table->integer('baggage_check_in')->nullable(); // وزن الحقائب المسجلة
            $table->integer('baggage_cabin')->nullable(); // وزن الحقائب المحمولة
            $table->boolean('is_available')->default(true);
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('flight_id')->references('id')->on('flights')->onDelete('cascade');
            $table->unique(['flight_id', 'seat_number']);
        });

        // جدول بيانات المسافرين
        Schema::create('booking_passengers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('flight_id')->nullable();
            $table->bigInteger('flight_seat_id')->nullable();
            $table->bigInteger('booking_id')->nullable();
            $table->string('seat_type')->nullable();
            $table->string('email')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('phone')->nullable();
            $table->dateTime('dob')->nullable(); // تاريخ الميلاد
            $table->decimal('price', 12, 2)->nullable();
            $table->string('id_card')->nullable(); // رقم الهوية/جواز السفر
            $table->string('nationality')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('object_model', 30);
            $table->bigInteger('object_id')->nullable();
            
            $table->index('booking_id');
            $table->index(['object_model', 'object_id']);
            
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // جدول شروط الرحلات
        Schema::create('flight_terms', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('term_id')->nullable();
            $table->bigInteger('target_id')->nullable(); // flight_id
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        // جدول حجوزات الرحلات
        Schema::create('flight_bookings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id');
            $table->bigInteger('flight_id');
            $table->string('booking_reference')->unique();
            $table->decimal('total_price', 12, 2);
            $table->integer('passengers_count');
            $table->json('passenger_details'); // تفاصيل المسافرين
            $table->enum('status', ['pending', 'confirmed', 'cancelled', 'completed'])->default('pending');
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending');
            $table->datetime('booking_date');
            $table->text('special_requests')->nullable();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('flight_id')->references('id')->on('flights');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flight_bookings');
        Schema::dropIfExists('flight_terms');
        Schema::dropIfExists('booking_passengers');
        Schema::dropIfExists('flight_seats');
    }
};
