import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:travel_offers/data/model/flight/flight_model.dart';
import 'package:travel_offers/utils/api_constants.dart';

class FlightService {
  static const String baseUrl = ApiConstants.baseUrl;

  // Get flights with filters
  static Future<FlightResponse> getFlights({
    String? fromAirport,
    String? toAirport,
    String? departureDate,
    String? airlineId,
    double? minPrice,
    double? maxPrice,
    String sortBy = 'departure_time',
    String sortOrder = 'asc',
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'per_page': perPage.toString(),
        'sort_by': sortBy,
        'sort_order': sortOrder,
      };

      if (fromAirport != null) queryParams['from_airport'] = fromAirport;
      if (toAirport != null) queryParams['to_airport'] = toAirport;
      if (departureDate != null) queryParams['departure_date'] = departureDate;
      if (airlineId != null) queryParams['airline_id'] = airlineId;
      if (minPrice != null) queryParams['min_price'] = minPrice.toString();
      if (maxPrice != null) queryParams['max_price'] = maxPrice.toString();

      final uri = Uri.parse('$baseUrl/api/flights').replace(queryParameters: queryParams);
      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return FlightResponse.fromJson(data);
      } else {
        throw Exception('Failed to load flights: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading flights: $e');
    }
  }

  // Get flight details
  static Future<FlightModel> getFlightDetails(int flightId) async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/api/flights/$flightId'));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return FlightModel.fromJson(data['data']);
        } else {
          throw Exception(data['message'] ?? 'Failed to load flight details');
        }
      } else {
        throw Exception('Failed to load flight details: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading flight details: $e');
    }
  }

  // Get airlines list
  static Future<List<AirlineModel>> getAirlines() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/api/flights/airlines/list'));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return (data['data'] as List)
              .map((airline) => AirlineModel.fromJson(airline))
              .toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to load airlines');
        }
      } else {
        throw Exception('Failed to load airlines: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading airlines: $e');
    }
  }

  // Get airports list
  static Future<List<AirportModel>> getAirports({
    String? search,
    String? country,
  }) async {
    try {
      final Map<String, String> queryParams = {};
      if (search != null) queryParams['search'] = search;
      if (country != null) queryParams['country'] = country;

      final uri = Uri.parse('$baseUrl/api/flights/airports/list').replace(queryParameters: queryParams);
      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return (data['data'] as List)
              .map((airport) => AirportModel.fromJson(airport))
              .toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to load airports');
        }
      } else {
        throw Exception('Failed to load airports: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading airports: $e');
    }
  }

  // Book flight
  static Future<FlightBookingResponse> bookFlight({
    required String token,
    required int flightId,
    required List<PassengerData> passengers,
    String? specialRequests,
  }) async {
    try {
      final Map<String, dynamic> requestBody = {
        'flight_id': flightId,
        'passengers': passengers.map((p) => p.toJson()).toList(),
        if (specialRequests != null) 'special_requests': specialRequests,
      };

      final response = await http.post(
        Uri.parse('$baseUrl/api/flights/book'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestBody),
      );

      final data = json.decode(response.body);

      if (response.statusCode == 200 && data['success']) {
        return FlightBookingResponse.fromJson(data);
      } else {
        throw Exception(data['message'] ?? 'Failed to book flight');
      }
    } catch (e) {
      throw Exception('Error booking flight: $e');
    }
  }

  // Get user's flight bookings
  static Future<List<FlightBookingModel>> getMyBookings({
    required String token,
    String? status,
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'per_page': perPage.toString(),
      };
      if (status != null) queryParams['status'] = status;

      final uri = Uri.parse('$baseUrl/api/flights/my-bookings').replace(queryParameters: queryParams);
      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return (data['data'] as List)
              .map((booking) => FlightBookingModel.fromJson(booking))
              .toList();
        } else {
          throw Exception(data['message'] ?? 'Failed to load bookings');
        }
      } else {
        throw Exception('Failed to load bookings: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading bookings: $e');
    }
  }
}

class FlightResponse {
  final List<FlightModel> flights;
  final PaginationData pagination;

  FlightResponse({
    required this.flights,
    required this.pagination,
  });

  factory FlightResponse.fromJson(Map<String, dynamic> json) {
    return FlightResponse(
      flights: (json['data'] as List)
          .map((flight) => FlightModel.fromJson(flight))
          .toList(),
      pagination: PaginationData.fromJson(json['pagination']),
    );
  }
}

class PaginationData {
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;

  PaginationData({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
  });

  factory PaginationData.fromJson(Map<String, dynamic> json) {
    return PaginationData(
      currentPage: json['current_page'] ?? 1,
      lastPage: json['last_page'] ?? 1,
      perPage: json['per_page'] ?? 20,
      total: json['total'] ?? 0,
    );
  }
}

class PassengerData {
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final DateTime dob;
  final String idCard;
  final String nationality;
  final String gender;
  final int seatId;

  PassengerData({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.dob,
    required this.idCard,
    required this.nationality,
    required this.gender,
    required this.seatId,
  });

  Map<String, dynamic> toJson() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'dob': dob.toIso8601String(),
      'id_card': idCard,
      'nationality': nationality,
      'gender': gender,
      'seat_id': seatId,
    };
  }
}

class FlightBookingResponse {
  final String bookingReference;
  final double totalPrice;
  final int bookingId;
  final String message;

  FlightBookingResponse({
    required this.bookingReference,
    required this.totalPrice,
    required this.bookingId,
    required this.message,
  });

  factory FlightBookingResponse.fromJson(Map<String, dynamic> json) {
    return FlightBookingResponse(
      bookingReference: json['data']['booking_reference'] ?? '',
      totalPrice: (json['data']['total_price'] ?? 0).toDouble(),
      bookingId: json['data']['booking_id'] ?? 0,
      message: json['message'] ?? '',
    );
  }
}

class FlightBookingModel {
  final int id;
  final String bookingReference;
  final double totalPrice;
  final int passengersCount;
  final String status;
  final String paymentStatus;
  final DateTime bookingDate;
  final FlightModel flight;

  FlightBookingModel({
    required this.id,
    required this.bookingReference,
    required this.totalPrice,
    required this.passengersCount,
    required this.status,
    required this.paymentStatus,
    required this.bookingDate,
    required this.flight,
  });

  factory FlightBookingModel.fromJson(Map<String, dynamic> json) {
    return FlightBookingModel(
      id: json['id'] ?? 0,
      bookingReference: json['booking_reference'] ?? '',
      totalPrice: (json['total_price'] ?? 0).toDouble(),
      passengersCount: json['passengers_count'] ?? 0,
      status: json['status'] ?? '',
      paymentStatus: json['payment_status'] ?? '',
      bookingDate: DateTime.parse(json['booking_date']),
      flight: FlightModel.fromJson(json['flight'] ?? {}),
    );
  }
}
