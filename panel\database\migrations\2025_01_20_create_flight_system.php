<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول شركات الطيران
        Schema::create('airlines', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->string('code')->unique()->nullable();
            $table->integer('image_id')->nullable();
            $table->string('country', 20)->nullable();
            $table->text('description')->nullable();
            $table->string('status', 30)->nullable()->default('publish');
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        // جدول المطارات
        Schema::create('airports', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->string('code')->unique();
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country', 20)->nullable();
            $table->integer('location_id')->nullable();
            $table->text('description')->nullable();
            $table->string('map_lat', 20)->nullable();
            $table->string('map_lng', 20)->nullable();
            $table->integer('map_zoom')->nullable();
            $table->string('timezone', 50)->nullable();
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->string('status', 30)->nullable()->default('publish');
            $table->timestamps();
        });

        // جدول أنواع المقاعد
        Schema::create('seat_types', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('code')->unique();
            $table->string('name')->nullable();
            $table->string('name_ar')->nullable();
            $table->text('description')->nullable();
            $table->json('features')->nullable(); // مميزات المقعد
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        // جدول الرحلات
        Schema::create('flights', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('title')->nullable();
            $table->string('code')->nullable();
            $table->string('flight_number')->nullable();
            $table->decimal('review_score', 2, 1)->nullable();
            $table->dateTime('departure_time')->nullable();
            $table->dateTime('arrival_time')->nullable();
            $table->float('duration')->nullable(); // بالدقائق
            $table->decimal('min_price', 12, 2)->nullable();
            $table->integer('airport_to')->nullable();
            $table->integer('airport_from')->nullable();
            $table->integer('airline_id')->nullable();
            $table->string('aircraft_type')->nullable();
            $table->string('status', 50)->nullable()->default('scheduled');
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('airline_id')->references('id')->on('airlines');
            $table->foreign('airport_from')->references('id')->on('airports');
            $table->foreign('airport_to')->references('id')->on('airports');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flights');
        Schema::dropIfExists('seat_types');
        Schema::dropIfExists('airports');
        Schema::dropIfExists('airlines');
    }
};
