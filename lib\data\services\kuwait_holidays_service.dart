class KuwaitHolidayModel {
  final int id;
  final String nameEn;
  final String nameAr;
  final DateTime date;
  final HolidayType type;
  final String description;
  final String descriptionAr;
  final bool isOfficial;
  final int durationDays;
  final String? specialOffers;
  final List<String> traditions;
  final String? imageUrl;

  KuwaitHolidayModel({
    required this.id,
    required this.nameEn,
    required this.nameAr,
    required this.date,
    required this.type,
    required this.description,
    required this.descriptionAr,
    required this.isOfficial,
    this.durationDays = 1,
    this.specialOffers,
    this.traditions = const [],
    this.imageUrl,
  });

  factory KuwaitHolidayModel.fromJson(Map<String, dynamic> json) {
    return KuwaitHolidayModel(
      id: json['id'] ?? 0,
      nameEn: json['name_en'] ?? '',
      nameAr: json['name_ar'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      type: HolidayType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => HolidayType.national,
      ),
      description: json['description'] ?? '',
      descriptionAr: json['description_ar'] ?? '',
      isOfficial: json['is_official'] ?? false,
      durationDays: json['duration_days'] ?? 1,
      specialOffers: json['special_offers'],
      traditions: (json['traditions'] as List<dynamic>?)
          ?.map((item) => item.toString())
          .toList() ?? [],
      imageUrl: json['image_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name_en': nameEn,
      'name_ar': nameAr,
      'date': date.toIso8601String(),
      'type': type.toString().split('.').last,
      'description': description,
      'description_ar': descriptionAr,
      'is_official': isOfficial,
      'duration_days': durationDays,
      'special_offers': specialOffers,
      'traditions': traditions,
      'image_url': imageUrl,
    };
  }

  String get displayName => nameAr.isNotEmpty ? nameAr : nameEn;
  String get displayDescription => descriptionAr.isNotEmpty ? descriptionAr : description;

  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }

  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(Duration(days: 6));
    return date.isAfter(startOfWeek) && date.isBefore(endOfWeek);
  }

  bool get isThisMonth {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  int get daysUntil {
    final now = DateTime.now();
    return date.difference(now).inDays;
  }

  String get daysUntilDisplay {
    final days = daysUntil;
    if (days == 0) return 'اليوم';
    if (days == 1) return 'غداً';
    if (days < 0) return 'انتهى';
    return 'خلال $days يوم';
  }
}

enum HolidayType {
  national,     // عطلة وطنية
  religious,    // عطلة دينية
  cultural,     // مناسبة ثقافية
  seasonal,     // موسمية
  international // عالمية
}

class KuwaitHolidaysService {
  static List<KuwaitHolidayModel> getKuwaitHolidays(int year) {
    return [
      // العطل الوطنية الثابتة
      KuwaitHolidayModel(
        id: 1,
        nameEn: 'New Year\'s Day',
        nameAr: 'رأس السنة الميلادية',
        date: DateTime(year, 1, 1),
        type: HolidayType.international,
        description: 'New Year celebration',
        descriptionAr: 'احتفال برأس السنة الميلادية',
        isOfficial: true,
        traditions: ['الاحتفالات', 'الألعاب النارية', 'التجمعات العائلية'],
        imageUrl: 'assets/images/holidays/new_year.jpg',
      ),

      KuwaitHolidayModel(
        id: 2,
        nameEn: 'Kuwait National Day',
        nameAr: 'العيد الوطني الكويتي',
        date: DateTime(year, 2, 25),
        type: HolidayType.national,
        description: 'Kuwait National Day celebrating independence',
        descriptionAr: 'العيد الوطني الكويتي احتفالاً بالاستقلال',
        isOfficial: true,
        durationDays: 2,
        specialOffers: 'خصومات خاصة على الرحلات والفعاليات',
        traditions: [
          'رفع العلم الكويتي',
          'الاستعراضات العسكرية',
          'الألعاب النارية',
          'الفعاليات الثقافية',
          'توزيع الحلويات'
        ],
        imageUrl: 'assets/images/holidays/national_day.jpg',
      ),

      KuwaitHolidayModel(
        id: 3,
        nameEn: 'Liberation Day',
        nameAr: 'يوم التحرير',
        date: DateTime(year, 2, 26),
        type: HolidayType.national,
        description: 'Liberation Day commemorating the end of Iraqi occupation',
        descriptionAr: 'يوم التحرير إحياءً لذكرى انتهاء الاحتلال العراقي',
        isOfficial: true,
        traditions: [
          'الاحتفالات الوطنية',
          'زيارة المتاحف',
          'الفعاليات التراثية',
          'العروض الجوية'
        ],
        imageUrl: 'assets/images/holidays/liberation_day.jpg',
      ),

      // العطل الدينية (تواريخ تقريبية - تحتاج تحديث سنوي)
      KuwaitHolidayModel(
        id: 4,
        nameEn: 'Ramadan',
        nameAr: 'شهر رمضان المبارك',
        date: _getRamadanDate(year),
        type: HolidayType.religious,
        description: 'Holy month of Ramadan',
        descriptionAr: 'شهر رمضان المبارك شهر الصيام والعبادة',
        isOfficial: true,
        durationDays: 30,
        specialOffers: 'عروض رمضانية خاصة على الإفطار والسحور',
        traditions: [
          'الصيام من الفجر للمغرب',
          'صلاة التراويح',
          'الإفطار الجماعي',
          'قراءة القرآن',
          'الزكاة والصدقات',
          'القرقيعان للأطفال'
        ],
        imageUrl: 'assets/images/holidays/ramadan.jpg',
      ),

      KuwaitHolidayModel(
        id: 5,
        nameEn: 'Eid Al-Fitr',
        nameAr: 'عيد الفطر المبارك',
        date: _getEidAlFitrDate(year),
        type: HolidayType.religious,
        description: 'Eid Al-Fitr celebrating the end of Ramadan',
        descriptionAr: 'عيد الفطر المبارك احتفالاً بانتهاء شهر رمضان',
        isOfficial: true,
        durationDays: 3,
        specialOffers: 'عروض العيد الخاصة على السفر والفعاليات',
        traditions: [
          'صلاة العيد',
          'زكاة الفطر',
          'العيدية للأطفال',
          'زيارة الأهل والأصدقاء',
          'الحلويات والمأكولات الخاصة',
          'الملابس الجديدة'
        ],
        imageUrl: 'assets/images/holidays/eid_fitr.jpg',
      ),

      KuwaitHolidayModel(
        id: 6,
        nameEn: 'Arafat Day',
        nameAr: 'يوم عرفة',
        date: _getArafatDate(year),
        type: HolidayType.religious,
        description: 'Day of Arafat during Hajj pilgrimage',
        descriptionAr: 'يوم عرفة من أيام الحج المباركة',
        isOfficial: true,
        traditions: [
          'الصيام لغير الحجاج',
          'الدعاء والذكر',
          'الأعمال الخيرية'
        ],
        imageUrl: 'assets/images/holidays/arafat.jpg',
      ),

      KuwaitHolidayModel(
        id: 7,
        nameEn: 'Eid Al-Adha',
        nameAr: 'عيد الأضحى المبارك',
        date: _getEidAlAdhaDate(year),
        type: HolidayType.religious,
        description: 'Eid Al-Adha commemorating Abraham\'s sacrifice',
        descriptionAr: 'عيد الأضحى المبارك إحياءً لذكرى تضحية إبراهيم عليه السلام',
        isOfficial: true,
        durationDays: 4,
        specialOffers: 'عروض الحج والعمرة وعروض العيد',
        traditions: [
          'صلاة العيد',
          'ذبح الأضاحي',
          'توزيع اللحوم على الفقراء',
          'زيارة الأهل والأصدقاء',
          'العيدية للأطفال'
        ],
        imageUrl: 'assets/images/holidays/eid_adha.jpg',
      ),

      KuwaitHolidayModel(
        id: 8,
        nameEn: 'Islamic New Year',
        nameAr: 'رأس السنة الهجرية',
        date: _getIslamicNewYearDate(year),
        type: HolidayType.religious,
        description: 'Islamic New Year',
        descriptionAr: 'رأس السنة الهجرية',
        isOfficial: true,
        traditions: [
          'الدعاء والذكر',
          'قراءة السيرة النبوية',
          'الأعمال الخيرية'
        ],
        imageUrl: 'assets/images/holidays/islamic_new_year.jpg',
      ),

      KuwaitHolidayModel(
        id: 9,
        nameEn: 'Prophet Muhammad\'s Birthday',
        nameAr: 'المولد النبوي الشريف',
        date: _getMawlidDate(year),
        type: HolidayType.religious,
        description: 'Prophet Muhammad\'s Birthday',
        descriptionAr: 'المولد النبوي الشريف',
        isOfficial: true,
        traditions: [
          'قراءة السيرة النبوية',
          'الأناشيد الدينية',
          'توزيع الحلويات',
          'الفعاليات الدينية'
        ],
        imageUrl: 'assets/images/holidays/mawlid.jpg',
      ),

      KuwaitHolidayModel(
        id: 10,
        nameEn: 'Isra and Mi\'raj',
        nameAr: 'الإسراء والمعراج',
        date: _getIsraMirajDate(year),
        type: HolidayType.religious,
        description: 'Night Journey and Ascension of Prophet Muhammad',
        descriptionAr: 'ذكرى الإسراء والمعراج',
        isOfficial: true,
        traditions: [
          'الصلاة والدعاء',
          'قراءة قصة الإسراء والمعراج',
          'الفعاليات الدينية'
        ],
        imageUrl: 'assets/images/holidays/isra_miraj.jpg',
      ),

      // المناسبات الثقافية والموسمية
      KuwaitHolidayModel(
        id: 11,
        nameEn: 'Hala February Festival',
        nameAr: 'مهرجان هلا فبراير',
        date: DateTime(year, 2, 1),
        type: HolidayType.cultural,
        description: 'Annual cultural festival in February',
        descriptionAr: 'مهرجان ثقافي سنوي يقام في شهر فبراير',
        isOfficial: false,
        durationDays: 28,
        specialOffers: 'فعاليات وعروض ثقافية متنوعة',
        traditions: [
          'العروض الثقافية',
          'المعارض الفنية',
          'الحفلات الموسيقية',
          'الأنشطة التراثية',
          'المسابقات'
        ],
        imageUrl: 'assets/images/holidays/hala_february.jpg',
      ),

      KuwaitHolidayModel(
        id: 12,
        nameEn: 'Kuwait Summer Festival',
        nameAr: 'مهرجان الكويت الصيفي',
        date: DateTime(year, 7, 1),
        type: HolidayType.seasonal,
        description: 'Summer festival with various activities',
        descriptionAr: 'مهرجان صيفي بأنشطة وفعاليات متنوعة',
        isOfficial: false,
        durationDays: 60,
        specialOffers: 'عروض صيفية على الأنشطة والرحلات',
        traditions: [
          'الأنشطة الترفيهية',
          'المسابقات',
          'العروض التجارية',
          'الفعاليات العائلية'
        ],
        imageUrl: 'assets/images/holidays/summer_festival.jpg',
      ),
    ];
  }

  static List<KuwaitHolidayModel> getUpcomingHolidays({int limit = 5}) {
    final now = DateTime.now();
    final currentYear = now.year;
    final nextYear = currentYear + 1;
    
    final holidays = [
      ...getKuwaitHolidays(currentYear),
      ...getKuwaitHolidays(nextYear),
    ];

    return holidays
        .where((holiday) => holiday.date.isAfter(now))
        .toList()
        ..sort((a, b) => a.date.compareTo(b.date))
        ..take(limit).toList();
  }

  static List<KuwaitHolidayModel> getHolidaysThisMonth() {
    final now = DateTime.now();
    return getKuwaitHolidays(now.year)
        .where((holiday) => holiday.isThisMonth)
        .toList();
  }

  static KuwaitHolidayModel? getTodaysHoliday() {
    final now = DateTime.now();
    try {
      return getKuwaitHolidays(now.year)
          .firstWhere((holiday) => holiday.isToday);
    } catch (e) {
      return null;
    }
  }

  static List<KuwaitHolidayModel> getHolidaysByType(HolidayType type) {
    final now = DateTime.now();
    return getKuwaitHolidays(now.year)
        .where((holiday) => holiday.type == type)
        .toList();
  }

  static bool isHoliday(DateTime date) {
    return getKuwaitHolidays(date.year)
        .any((holiday) => 
            holiday.date.year == date.year &&
            holiday.date.month == date.month &&
            holiday.date.day == date.day);
  }

  static List<String> getHolidayTips(KuwaitHolidayModel holiday) {
    List<String> tips = [];

    switch (holiday.type) {
      case HolidayType.national:
        tips.addAll([
          'احجز مبكراً للفعاليات الوطنية',
          'توقع ازدحاماً في المواصلات',
          'استمتع بالعروض الخاصة',
        ]);
        break;
      case HolidayType.religious:
        if (holiday.nameAr.contains('رمضان')) {
          tips.addAll([
            'مواعيد العمل مختلفة في رمضان',
            'احجز إفطارك مبكراً',
            'استمتع بالأجواء الرمضانية',
          ]);
        } else if (holiday.nameAr.contains('عيد')) {
          tips.addAll([
            'احجز مبكراً للسفر في العيد',
            'استمتع بالعروض الخاصة',
            'خطط لزيارة الأهل والأصدقاء',
          ]);
        }
        break;
      case HolidayType.cultural:
        tips.addAll([
          'استكشف الفعاليات الثقافية',
          'احضر العروض الفنية',
          'شارك في الأنشطة التراثية',
        ]);
        break;
      case HolidayType.seasonal:
        tips.addAll([
          'استمتع بالأنشطة الموسمية',
          'احجز مبكراً للفعاليات الشائعة',
          'استفد من العروض الخاصة',
        ]);
        break;
      default:
        break;
    }

    return tips;
  }

  // Helper methods for calculating Islamic dates (approximate)
  static DateTime _getRamadanDate(int year) {
    // This is approximate - should use proper Islamic calendar calculation
    final baseDate = DateTime(2024, 3, 11); // Ramadan 2024 start
    final yearDiff = year - 2024;
    return baseDate.add(Duration(days: yearDiff * -11)); // Islamic year is ~11 days shorter
  }

  static DateTime _getEidAlFitrDate(int year) {
    return _getRamadanDate(year).add(Duration(days: 29));
  }

  static DateTime _getArafatDate(int year) {
    final baseDate = DateTime(2024, 6, 15); // Arafat 2024
    final yearDiff = year - 2024;
    return baseDate.add(Duration(days: yearDiff * -11));
  }

  static DateTime _getEidAlAdhaDate(int year) {
    return _getArafatDate(year).add(Duration(days: 1));
  }

  static DateTime _getIslamicNewYearDate(int year) {
    final baseDate = DateTime(2024, 7, 7); // Islamic New Year 2024
    final yearDiff = year - 2024;
    return baseDate.add(Duration(days: yearDiff * -11));
  }

  static DateTime _getMawlidDate(int year) {
    final baseDate = DateTime(2024, 9, 15); // Mawlid 2024
    final yearDiff = year - 2024;
    return baseDate.add(Duration(days: yearDiff * -11));
  }

  static DateTime _getIsraMirajDate(int year) {
    final baseDate = DateTime(2024, 2, 8); // Isra Mi'raj 2024
    final yearDiff = year - 2024;
    return baseDate.add(Duration(days: yearDiff * -11));
  }
}
