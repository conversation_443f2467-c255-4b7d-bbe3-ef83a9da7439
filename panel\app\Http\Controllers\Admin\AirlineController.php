<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Airline;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class AirlineController extends Controller
{
    /**
     * Display a listing of airlines
     */
    public function index(Request $request)
    {
        $query = Airline::query();

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('country')) {
            $query->where('country', $request->country);
        }

        $airlines = $query->orderBy('name')->paginate(20);

        return view('admin.airlines.index', compact('airlines'));
    }

    /**
     * Show the form for creating a new airline
     */
    public function create()
    {
        return view('admin.airlines.create');
    }

    /**
     * Store a newly created airline
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:airlines,code',
            'country' => 'nullable|string|max:20',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:publish,draft'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $data = $request->only(['name', 'code', 'country', 'description', 'status']);
        $data['create_user'] = auth()->id();

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('airlines', 'public');
            $data['image_id'] = $logoPath;
        }

        Airline::create($data);

        return redirect()->route('admin.airlines.index')
                        ->with('success', 'تم إنشاء شركة الطيران بنجاح');
    }

    /**
     * Display the specified airline
     */
    public function show(Airline $airline)
    {
        $airline->load(['flights' => function($query) {
            $query->orderBy('departure_time', 'desc')->limit(10);
        }]);

        return view('admin.airlines.show', compact('airline'));
    }

    /**
     * Show the form for editing the specified airline
     */
    public function edit(Airline $airline)
    {
        return view('admin.airlines.edit', compact('airline'));
    }

    /**
     * Update the specified airline
     */
    public function update(Request $request, Airline $airline)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:airlines,code,' . $airline->id,
            'country' => 'nullable|string|max:20',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:publish,draft'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $data = $request->only(['name', 'code', 'country', 'description', 'status']);
        $data['update_user'] = auth()->id();

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($airline->image_id) {
                Storage::disk('public')->delete($airline->image_id);
            }
            
            $logoPath = $request->file('logo')->store('airlines', 'public');
            $data['image_id'] = $logoPath;
        }

        $airline->update($data);

        return redirect()->route('admin.airlines.index')
                        ->with('success', 'تم تحديث شركة الطيران بنجاح');
    }

    /**
     * Remove the specified airline
     */
    public function destroy(Airline $airline)
    {
        // Check if airline has flights
        if ($airline->flights()->exists()) {
            return redirect()->back()
                           ->with('error', 'لا يمكن حذف شركة الطيران لوجود رحلات مرتبطة بها');
        }

        // Delete logo
        if ($airline->image_id) {
            Storage::disk('public')->delete($airline->image_id);
        }

        $airline->delete();

        return redirect()->route('admin.airlines.index')
                        ->with('success', 'تم حذف شركة الطيران بنجاح');
    }
}
