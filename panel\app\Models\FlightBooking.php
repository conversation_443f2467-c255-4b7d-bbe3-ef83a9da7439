<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FlightBooking extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'flight_id',
        'booking_reference',
        'total_price',
        'passengers_count',
        'passenger_details',
        'status',
        'payment_status',
        'booking_date',
        'special_requests'
    ];

    protected $casts = [
        'total_price' => 'decimal:2',
        'passenger_details' => 'array',
        'booking_date' => 'datetime'
    ];

    protected $appends = ['status_text', 'payment_status_text'];

    /**
     * Get status text
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'pending' => 'في الانتظار',
            'confirmed' => 'مؤكد',
            'cancelled' => 'ملغي',
            'completed' => 'مكتمل',
            default => 'غير محدد'
        };
    }

    /**
     * Get payment status text
     */
    public function getPaymentStatusTextAttribute()
    {
        return match($this->payment_status) {
            'pending' => 'في الانتظار',
            'paid' => 'مدفوع',
            'failed' => 'فشل',
            'refunded' => 'مسترد',
            default => 'غير محدد'
        };
    }

    /**
     * Generate unique booking reference
     */
    public static function generateBookingReference()
    {
        do {
            $reference = 'FL' . strtoupper(substr(uniqid(), -8));
        } while (self::where('booking_reference', $reference)->exists());

        return $reference;
    }

    /**
     * Get user
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get flight
     */
    public function flight()
    {
        return $this->belongsTo(Flight::class);
    }

    /**
     * Get passengers
     */
    public function passengers()
    {
        return $this->hasMany(BookingPassenger::class, 'booking_id');
    }

    /**
     * Get payment transactions
     */
    public function paymentTransactions()
    {
        // TODO: Create PaymentTransaction model if needed
        return collect([]);
    }

    /**
     * Check if booking is confirmed
     */
    public function isConfirmed()
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if booking is paid
     */
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }

    /**
     * Confirm booking
     */
    public function confirm()
    {
        $this->update(['status' => 'confirmed']);
    }

    /**
     * Cancel booking
     */
    public function cancel()
    {
        $this->update(['status' => 'cancelled']);
        
        // Release seats
        $this->passengers->each(function ($passenger) {
            if ($passenger->flightSeat) {
                $passenger->flightSeat->release();
            }
        });
    }

    /**
     * Mark as paid
     */
    public function markAsPaid()
    {
        $this->update(['payment_status' => 'paid']);
        
        if ($this->status === 'pending') {
            $this->confirm();
        }
    }

    /**
     * Scope for confirmed bookings
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope for paid bookings
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    /**
     * Scope by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
