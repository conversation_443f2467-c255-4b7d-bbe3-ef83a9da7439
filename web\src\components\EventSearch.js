import React, { useState, useEffect } from 'react';
import { Card, Form, Button, Row, Col, Badge } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import Select from 'react-select';
import "react-datepicker/dist/react-datepicker.css";

const EventSearch = ({ onSearch, loading }) => {
  const [searchData, setSearchData] = useState({
    search: '',
    eventType: '',
    venue: null,
    city: '',
    dateFrom: null,
    dateTo: null,
    minPrice: '',
    maxPrice: '',
    isFeatured: false
  });

  const [venues, setVenues] = useState([]);
  const [cities, setCities] = useState([]);

  useEffect(() => {
    loadVenues();
  }, []);

  const loadVenues = async () => {
    try {
      const response = await fetch('/api/events/venues/list');
      const data = await response.json();
      if (data.success) {
        const venueOptions = data.data.map(venue => ({
          value: venue.id,
          label: `${venue.name_ar || venue.name} - ${venue.city}`,
          city: venue.city
        }));
        setVenues(venueOptions);
        
        // استخراج المدن الفريدة
        const uniqueCities = [...new Set(data.data.map(venue => venue.city))];
        setCities(uniqueCities);
      }
    } catch (error) {
      console.error('Error loading venues:', error);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    
    const searchParams = {
      ...(searchData.search && { search: searchData.search }),
      ...(searchData.eventType && { event_type: searchData.eventType }),
      ...(searchData.venue && { venue_id: searchData.venue.value }),
      ...(searchData.city && { city: searchData.city }),
      ...(searchData.dateFrom && { date_from: searchData.dateFrom.toISOString().split('T')[0] }),
      ...(searchData.dateTo && { date_to: searchData.dateTo.toISOString().split('T')[0] }),
      ...(searchData.minPrice && { min_price: searchData.minPrice }),
      ...(searchData.maxPrice && { max_price: searchData.maxPrice }),
      ...(searchData.isFeatured && { is_featured: true })
    };

    onSearch(searchParams);
  };

  const clearFilters = () => {
    setSearchData({
      search: '',
      eventType: '',
      venue: null,
      city: '',
      dateFrom: null,
      dateTo: null,
      minPrice: '',
      maxPrice: '',
      isFeatured: false
    });
    onSearch({});
  };

  const eventTypes = [
    { value: '', label: 'جميع الأنواع' },
    { value: 'football', label: 'كرة القدم' },
    { value: 'basketball', label: 'كرة السلة' },
    { value: 'concert', label: 'حفلات موسيقية' },
    { value: 'conference', label: 'مؤتمرات' },
    { value: 'other', label: 'أخرى' }
  ];

  return (
    <Card className="mb-4">
      <Card.Header>
        <h5 className="mb-0">
          <i className="fas fa-calendar-alt me-2"></i>
          البحث في الفعاليات والمباريات
        </h5>
      </Card.Header>
      <Card.Body>
        <Form onSubmit={handleSearch}>
          {/* البحث النصي ونوع الفعالية */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>البحث</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="ابحث في أسماء الفعاليات..."
                  value={searchData.search}
                  onChange={(e) => setSearchData(prev => ({ ...prev, search: e.target.value }))}
                />
              </Form.Group>
            </Col>
            
            <Col md={6}>
              <Form.Group>
                <Form.Label>نوع الفعالية</Form.Label>
                <Form.Select
                  value={searchData.eventType}
                  onChange={(e) => setSearchData(prev => ({ ...prev, eventType: e.target.value }))}
                >
                  {eventTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>

          {/* المكان والمدينة */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>المكان</Form.Label>
                <Select
                  value={searchData.venue}
                  onChange={(selected) => setSearchData(prev => ({ ...prev, venue: selected }))}
                  options={venues}
                  placeholder="اختر المكان"
                  isClearable
                  isSearchable
                  className="react-select-container"
                  classNamePrefix="react-select"
                />
              </Form.Group>
            </Col>
            
            <Col md={6}>
              <Form.Group>
                <Form.Label>المدينة</Form.Label>
                <Form.Select
                  value={searchData.city}
                  onChange={(e) => setSearchData(prev => ({ ...prev, city: e.target.value }))}
                >
                  <option value="">جميع المدن</option>
                  {cities.map(city => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>

          {/* التواريخ */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>من تاريخ</Form.Label>
                <DatePicker
                  selected={searchData.dateFrom}
                  onChange={(date) => setSearchData(prev => ({ ...prev, dateFrom: date }))}
                  dateFormat="dd/MM/yyyy"
                  className="form-control"
                  placeholderText="اختر تاريخ البداية"
                  isClearable
                />
              </Form.Group>
            </Col>
            
            <Col md={6}>
              <Form.Group>
                <Form.Label>إلى تاريخ</Form.Label>
                <DatePicker
                  selected={searchData.dateTo}
                  onChange={(date) => setSearchData(prev => ({ ...prev, dateTo: date }))}
                  minDate={searchData.dateFrom}
                  dateFormat="dd/MM/yyyy"
                  className="form-control"
                  placeholderText="اختر تاريخ النهاية"
                  isClearable
                />
              </Form.Group>
            </Col>
          </Row>

          {/* الأسعار والخيارات */}
          <Row className="mb-3">
            <Col md={3}>
              <Form.Group>
                <Form.Label>السعر الأدنى (د.ك)</Form.Label>
                <Form.Control
                  type="number"
                  min="0"
                  step="0.1"
                  placeholder="0"
                  value={searchData.minPrice}
                  onChange={(e) => setSearchData(prev => ({ ...prev, minPrice: e.target.value }))}
                />
              </Form.Group>
            </Col>
            
            <Col md={3}>
              <Form.Group>
                <Form.Label>السعر الأعلى (د.ك)</Form.Label>
                <Form.Control
                  type="number"
                  min="0"
                  step="0.1"
                  placeholder="1000"
                  value={searchData.maxPrice}
                  onChange={(e) => setSearchData(prev => ({ ...prev, maxPrice: e.target.value }))}
                />
              </Form.Group>
            </Col>
            
            <Col md={3} className="d-flex align-items-end">
              <Form.Check
                type="checkbox"
                label="الفعاليات المميزة فقط"
                checked={searchData.isFeatured}
                onChange={(e) => setSearchData(prev => ({ ...prev, isFeatured: e.target.checked }))}
                className="mb-3"
              />
            </Col>
            
            <Col md={3} className="d-flex align-items-end gap-2">
              <Button 
                type="submit" 
                variant="primary" 
                disabled={loading}
                className="flex-grow-1"
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    جاري البحث...
                  </>
                ) : (
                  <>
                    <i className="fas fa-search me-2"></i>
                    بحث
                  </>
                )}
              </Button>
              
              <Button 
                type="button" 
                variant="outline-secondary"
                onClick={clearFilters}
              >
                <i className="fas fa-times"></i>
              </Button>
            </Col>
          </Row>

          {/* عرض الفلاتر النشطة */}
          {(searchData.search || searchData.eventType || searchData.venue || searchData.city || 
            searchData.dateFrom || searchData.dateTo || searchData.minPrice || searchData.maxPrice || 
            searchData.isFeatured) && (
            <Row>
              <Col>
                <div className="d-flex flex-wrap gap-2 align-items-center">
                  <small className="text-muted">الفلاتر النشطة:</small>
                  
                  {searchData.search && (
                    <Badge bg="primary">البحث: {searchData.search}</Badge>
                  )}
                  
                  {searchData.eventType && (
                    <Badge bg="info">
                      النوع: {eventTypes.find(t => t.value === searchData.eventType)?.label}
                    </Badge>
                  )}
                  
                  {searchData.venue && (
                    <Badge bg="success">المكان: {searchData.venue.label}</Badge>
                  )}
                  
                  {searchData.city && (
                    <Badge bg="warning">المدينة: {searchData.city}</Badge>
                  )}
                  
                  {searchData.isFeatured && (
                    <Badge bg="danger">مميز</Badge>
                  )}
                </div>
              </Col>
            </Row>
          )}
        </Form>
      </Card.Body>
    </Card>
  );
};

export default EventSearch;
