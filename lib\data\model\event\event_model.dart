class EventModel {
  final int id;
  final String title;
  final String titleAr;
  final String slug;
  final String content;
  final String contentAr;
  final String? imageUrl;
  final String? bannerImageUrl;
  final VenueModel venue;
  final String eventType;
  final String organizer;
  final int? duration; // in minutes
  final String? startTime;
  final double? price;
  final double? salePrice;
  final bool isFeatured;
  final bool isInstant;
  final double? reviewScore;
  final String status;
  final List<TicketCategoryModel> ticketCategories;
  final List<String> gallery;
  final String? videoUrl;

  EventModel({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.slug,
    required this.content,
    required this.contentAr,
    this.imageUrl,
    this.bannerImageUrl,
    required this.venue,
    required this.eventType,
    required this.organizer,
    this.duration,
    this.startTime,
    this.price,
    this.salePrice,
    required this.isFeatured,
    required this.isInstant,
    this.reviewScore,
    required this.status,
    this.ticketCategories = const [],
    this.gallery = const [],
    this.videoUrl,
  });

  factory EventModel.fromJson(Map<String, dynamic> json) {
    return EventModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      titleAr: json['title_ar'] ?? '',
      slug: json['slug'] ?? '',
      content: json['content'] ?? '',
      contentAr: json['content_ar'] ?? '',
      imageUrl: json['image_url'],
      bannerImageUrl: json['banner_image_url'],
      venue: VenueModel.fromJson(json['venue'] ?? {}),
      eventType: json['event_type'] ?? 'football',
      organizer: json['organizer'] ?? '',
      duration: json['duration'],
      startTime: json['start_time'],
      price: json['price']?.toDouble(),
      salePrice: json['sale_price']?.toDouble(),
      isFeatured: json['is_featured'] ?? false,
      isInstant: json['is_instant'] ?? false,
      reviewScore: json['review_score']?.toDouble(),
      status: json['status'] ?? 'upcoming',
      ticketCategories: (json['ticket_categories'] as List<dynamic>?)
          ?.map((category) => TicketCategoryModel.fromJson(category))
          .toList() ?? [],
      gallery: (json['gallery'] as List<dynamic>?)
          ?.map((image) => image.toString())
          .toList() ?? [],
      videoUrl: json['video'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'title_ar': titleAr,
      'slug': slug,
      'content': content,
      'content_ar': contentAr,
      'image_url': imageUrl,
      'banner_image_url': bannerImageUrl,
      'venue': venue.toJson(),
      'event_type': eventType,
      'organizer': organizer,
      'duration': duration,
      'start_time': startTime,
      'price': price,
      'sale_price': salePrice,
      'is_featured': isFeatured,
      'is_instant': isInstant,
      'review_score': reviewScore,
      'status': status,
      'ticket_categories': ticketCategories.map((category) => category.toJson()).toList(),
      'gallery': gallery,
      'video': videoUrl,
    };
  }

  String get displayTitle {
    // Return Arabic title if available, otherwise English
    return titleAr.isNotEmpty ? titleAr : title;
  }

  String get displayContent {
    // Return Arabic content if available, otherwise English
    return contentAr.isNotEmpty ? contentAr : content;
  }

  String get statusText {
    switch (status) {
      case 'upcoming':
        return 'قادم';
      case 'ongoing':
        return 'جاري';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  String get eventTypeText {
    switch (eventType) {
      case 'football':
        return 'كرة قدم';
      case 'basketball':
        return 'كرة سلة';
      case 'concert':
        return 'حفل موسيقي';
      case 'conference':
        return 'مؤتمر';
      case 'other':
        return 'أخرى';
      default:
        return 'غير محدد';
    }
  }

  String? get durationFormatted {
    if (duration == null) return null;
    final hours = duration! ~/ 60;
    final minutes = duration! % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  double get finalPrice {
    return salePrice ?? price ?? 0.0;
  }

  bool get hasDiscount {
    return salePrice != null && price != null && salePrice! < price!;
  }

  bool get isBookable {
    return status == 'upcoming' && ticketCategories.any((category) => category.isAvailable);
  }
}

class VenueModel {
  final int id;
  final String name;
  final String nameAr;
  final String address;
  final String city;
  final String country;
  final int capacity;
  final double? latitude;
  final double? longitude;
  final List<String> facilities;
  final List<String> images;

  VenueModel({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.address,
    required this.city,
    required this.country,
    required this.capacity,
    this.latitude,
    this.longitude,
    this.facilities = const [],
    this.images = const [],
  });

  factory VenueModel.fromJson(Map<String, dynamic> json) {
    return VenueModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      nameAr: json['name_ar'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      country: json['country'] ?? '',
      capacity: json['capacity'] ?? 0,
      latitude: json['map_lat']?.toDouble(),
      longitude: json['map_lng']?.toDouble(),
      facilities: (json['facilities'] as List<dynamic>?)
          ?.map((facility) => facility.toString())
          .toList() ?? [],
      images: (json['images'] as List<dynamic>?)
          ?.map((image) => image.toString())
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_ar': nameAr,
      'address': address,
      'city': city,
      'country': country,
      'capacity': capacity,
      'map_lat': latitude,
      'map_lng': longitude,
      'facilities': facilities,
      'images': images,
    };
  }

  String get displayName {
    return nameAr.isNotEmpty ? nameAr : name;
  }

  String get cityCountry {
    return '$city, $country';
  }
}

class TicketCategoryModel {
  final int id;
  final String name;
  final String nameAr;
  final double price;
  final int totalQuantity;
  final int availableQuantity;
  final String? description;
  final String? descriptionAr;
  final List<String> benefits;
  final String? section;
  final String? rowRange;
  final bool isVip;

  TicketCategoryModel({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.price,
    required this.totalQuantity,
    required this.availableQuantity,
    this.description,
    this.descriptionAr,
    this.benefits = const [],
    this.section,
    this.rowRange,
    required this.isVip,
  });

  factory TicketCategoryModel.fromJson(Map<String, dynamic> json) {
    return TicketCategoryModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      nameAr: json['name_ar'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      totalQuantity: json['total_quantity'] ?? 0,
      availableQuantity: json['available_quantity'] ?? 0,
      description: json['description'],
      descriptionAr: json['description_ar'],
      benefits: (json['benefits'] as List<dynamic>?)
          ?.map((benefit) => benefit.toString())
          .toList() ?? [],
      section: json['section'],
      rowRange: json['row_range'],
      isVip: json['is_vip'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_ar': nameAr,
      'price': price,
      'total_quantity': totalQuantity,
      'available_quantity': availableQuantity,
      'description': description,
      'description_ar': descriptionAr,
      'benefits': benefits,
      'section': section,
      'row_range': rowRange,
      'is_vip': isVip,
    };
  }

  String get displayName {
    return nameAr.isNotEmpty ? nameAr : name;
  }

  String get displayDescription {
    return descriptionAr?.isNotEmpty == true ? descriptionAr! : (description ?? '');
  }

  bool get isAvailable {
    return availableQuantity > 0;
  }

  int get soldQuantity {
    return totalQuantity - availableQuantity;
  }

  double get soldPercentage {
    if (totalQuantity == 0) return 0.0;
    return (soldQuantity / totalQuantity) * 100;
  }
}
