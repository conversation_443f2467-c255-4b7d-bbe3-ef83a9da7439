import 'dart:convert';
import 'package:http/http.dart' as http;

enum PaymentMethod {
  knet,
  visa,
  mastercard,
  americanExpress,
  cash,
  bankTransfer,
}

enum PaymentStatus {
  pending,
  processing,
  success,
  failed,
  cancelled,
  refunded,
}

class PaymentMethodModel {
  final PaymentMethod type;
  final String nameEn;
  final String nameAr;
  final String icon;
  final bool isAvailable;
  final double? processingFee;
  final String? description;
  final String? descriptionAr;

  PaymentMethodModel({
    required this.type,
    required this.nameEn,
    required this.nameAr,
    required this.icon,
    required this.isAvailable,
    this.processingFee,
    this.description,
    this.descriptionAr,
  });

  String get displayName => nameAr.isNotEmpty ? nameAr : nameEn;
  String get displayDescription => descriptionAr?.isNotEmpty == true ? descriptionAr! : description ?? '';
}

class PaymentTransactionModel {
  final String transactionId;
  final String referenceNumber;
  final double amount;
  final String currency;
  final PaymentMethod method;
  final PaymentStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? failureReason;
  final String? failureReasonAr;
  final Map<String, dynamic>? metadata;

  PaymentTransactionModel({
    required this.transactionId,
    required this.referenceNumber,
    required this.amount,
    required this.currency,
    required this.method,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.failureReason,
    this.failureReasonAr,
    this.metadata,
  });

  factory PaymentTransactionModel.fromJson(Map<String, dynamic> json) {
    return PaymentTransactionModel(
      transactionId: json['transaction_id'] ?? '',
      referenceNumber: json['reference_number'] ?? '',
      amount: json['amount']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'KWD',
      method: PaymentMethod.values.firstWhere(
        (e) => e.toString().split('.').last == json['method'],
        orElse: () => PaymentMethod.knet,
      ),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at']) 
          : null,
      failureReason: json['failure_reason'],
      failureReasonAr: json['failure_reason_ar'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transaction_id': transactionId,
      'reference_number': referenceNumber,
      'amount': amount,
      'currency': currency,
      'method': method.toString().split('.').last,
      'status': status.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'failure_reason': failureReason,
      'failure_reason_ar': failureReasonAr,
      'metadata': metadata,
    };
  }

  String get amountDisplay => '${amount.toStringAsFixed(3)} $currency';
  
  String get statusDisplayAr {
    switch (status) {
      case PaymentStatus.pending:
        return 'في الانتظار';
      case PaymentStatus.processing:
        return 'قيد المعالجة';
      case PaymentStatus.success:
        return 'تم بنجاح';
      case PaymentStatus.failed:
        return 'فشل';
      case PaymentStatus.cancelled:
        return 'ملغي';
      case PaymentStatus.refunded:
        return 'مسترد';
    }
  }

  bool get isCompleted => status == PaymentStatus.success;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isPending => status == PaymentStatus.pending || status == PaymentStatus.processing;
}

class KuwaitPaymentService {
  static const String _baseUrl = 'https://api.payment-gateway.com'; // Replace with actual URL
  static const String _apiKey = 'YOUR_API_KEY'; // Replace with actual API key

  static List<PaymentMethodModel> getAvailablePaymentMethods() {
    return [
      PaymentMethodModel(
        type: PaymentMethod.knet,
        nameEn: 'K-Net',
        nameAr: 'كي نت',
        icon: 'assets/images/payment/knet.png',
        isAvailable: true,
        processingFee: 0.0,
        description: 'Pay using your local bank card',
        descriptionAr: 'ادفع باستخدام بطاقتك البنكية المحلية',
      ),
      PaymentMethodModel(
        type: PaymentMethod.visa,
        nameEn: 'Visa',
        nameAr: 'فيزا',
        icon: 'assets/images/payment/visa.png',
        isAvailable: true,
        processingFee: 0.5,
        description: 'Pay with Visa credit/debit card',
        descriptionAr: 'ادفع ببطاقة فيزا الائتمانية أو الخصم',
      ),
      PaymentMethodModel(
        type: PaymentMethod.mastercard,
        nameEn: 'Mastercard',
        nameAr: 'ماستركارد',
        icon: 'assets/images/payment/mastercard.png',
        isAvailable: true,
        processingFee: 0.5,
        description: 'Pay with Mastercard credit/debit card',
        descriptionAr: 'ادفع ببطاقة ماستركارد الائتمانية أو الخصم',
      ),
      PaymentMethodModel(
        type: PaymentMethod.americanExpress,
        nameEn: 'American Express',
        nameAr: 'أمريكان إكسبريس',
        icon: 'assets/images/payment/amex.png',
        isAvailable: true,
        processingFee: 1.0,
        description: 'Pay with American Express card',
        descriptionAr: 'ادفع ببطاقة أمريكان إكسبريس',
      ),
      PaymentMethodModel(
        type: PaymentMethod.cash,
        nameEn: 'Cash on Delivery',
        nameAr: 'الدفع نقداً',
        icon: 'assets/images/payment/cash.png',
        isAvailable: false, // Usually not available for travel bookings
        processingFee: 0.0,
        description: 'Pay cash when receiving service',
        descriptionAr: 'ادفع نقداً عند استلام الخدمة',
      ),
      PaymentMethodModel(
        type: PaymentMethod.bankTransfer,
        nameEn: 'Bank Transfer',
        nameAr: 'تحويل بنكي',
        icon: 'assets/images/payment/bank.png',
        isAvailable: true,
        processingFee: 0.0,
        description: 'Transfer money directly from your bank account',
        descriptionAr: 'حول المال مباشرة من حسابك البنكي',
      ),
    ];
  }

  static PaymentMethodModel? getPaymentMethod(PaymentMethod method) {
    try {
      return getAvailablePaymentMethods()
          .firstWhere((pm) => pm.type == method);
    } catch (e) {
      return null;
    }
  }

  static Future<PaymentTransactionModel> initiatePayment({
    required double amount,
    required PaymentMethod method,
    required String currency,
    required Map<String, dynamic> metadata,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/payments/initiate'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode({
          'amount': amount,
          'currency': currency,
          'method': method.toString().split('.').last,
          'metadata': metadata,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PaymentTransactionModel.fromJson(data);
      } else {
        throw Exception('Failed to initiate payment');
      }
    } catch (e) {
      // Return mock transaction for development
      return _createMockTransaction(amount, method, currency, metadata);
    }
  }

  static Future<PaymentTransactionModel> processKNetPayment({
    required String transactionId,
    required String bankCode,
    required String cardNumber,
    required String pin,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/payments/knet/process'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode({
          'transaction_id': transactionId,
          'bank_code': bankCode,
          'card_number': cardNumber,
          'pin': pin,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PaymentTransactionModel.fromJson(data);
      } else {
        throw Exception('K-Net payment failed');
      }
    } catch (e) {
      throw Exception('K-Net payment processing error: $e');
    }
  }

  static Future<PaymentTransactionModel> processCreditCardPayment({
    required String transactionId,
    required String cardNumber,
    required String expiryMonth,
    required String expiryYear,
    required String cvv,
    required String cardHolderName,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/payments/card/process'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode({
          'transaction_id': transactionId,
          'card_number': cardNumber,
          'expiry_month': expiryMonth,
          'expiry_year': expiryYear,
          'cvv': cvv,
          'card_holder_name': cardHolderName,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PaymentTransactionModel.fromJson(data);
      } else {
        throw Exception('Credit card payment failed');
      }
    } catch (e) {
      throw Exception('Credit card payment processing error: $e');
    }
  }

  static Future<PaymentTransactionModel> checkPaymentStatus(String transactionId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/payments/$transactionId/status'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PaymentTransactionModel.fromJson(data);
      } else {
        throw Exception('Failed to check payment status');
      }
    } catch (e) {
      throw Exception('Payment status check error: $e');
    }
  }

  static Future<PaymentTransactionModel> refundPayment({
    required String transactionId,
    required double amount,
    required String reason,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/payments/$transactionId/refund'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode({
          'amount': amount,
          'reason': reason,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PaymentTransactionModel.fromJson(data);
      } else {
        throw Exception('Refund failed');
      }
    } catch (e) {
      throw Exception('Refund processing error: $e');
    }
  }

  static List<String> getKuwaitBanks() {
    return [
      'البنك الوطني الكويتي - NBK',
      'بنك الخليج - Gulf Bank',
      'البنك التجاري الكويتي - CBK',
      'بنك الأهلي الكويتي - ABK',
      'بيت التمويل الكويتي - KFH',
      'بنك برقان - Burgan Bank',
      'البنك الصناعي الكويتي - KIB',
      'بنك وربة - Warba Bank',
      'البنك الكويتي التركي - KTB',
    ];
  }

  static String getBankCode(String bankName) {
    final bankCodes = {
      'البنك الوطني الكويتي - NBK': 'NBK',
      'بنك الخليج - Gulf Bank': 'GBK',
      'البنك التجاري الكويتي - CBK': 'CBK',
      'بنك الأهلي الكويتي - ABK': 'ABK',
      'بيت التمويل الكويتي - KFH': 'KFH',
      'بنك برقان - Burgan Bank': 'BURG',
      'البنك الصناعي الكويتي - KIB': 'KIB',
      'بنك وربة - Warba Bank': 'WARB',
      'البنك الكويتي التركي - KTB': 'KTB',
    };
    return bankCodes[bankName] ?? '';
  }

  static double calculateProcessingFee(PaymentMethod method, double amount) {
    final paymentMethod = getPaymentMethod(method);
    if (paymentMethod?.processingFee == null) return 0.0;
    
    return paymentMethod!.processingFee!;
  }

  static double calculateTotalAmount(double baseAmount, PaymentMethod method) {
    final processingFee = calculateProcessingFee(method, baseAmount);
    return baseAmount + processingFee;
  }

  static bool isPaymentMethodAvailable(PaymentMethod method) {
    final paymentMethod = getPaymentMethod(method);
    return paymentMethod?.isAvailable ?? false;
  }

  static List<String> getPaymentTips(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.knet:
        return [
          'تأكد من أن بطاقتك مفعلة للدفع الإلكتروني',
          'احتفظ برقم المرجع للمتابعة',
          'لا تشارك رقم PIN مع أحد',
          'تأكد من اتصالك بالإنترنت',
        ];
      case PaymentMethod.visa:
      case PaymentMethod.mastercard:
      case PaymentMethod.americanExpress:
        return [
          'تأكد من صحة بيانات البطاقة',
          'تأكد من وجود رصيد كافي',
          'احتفظ برقم المرجع',
          'قد تحتاج لرمز التحقق من البنك',
        ];
      case PaymentMethod.bankTransfer:
        return [
          'احتفظ بإيصال التحويل',
          'قد يستغرق التحويل 1-3 أيام عمل',
          'تأكد من صحة رقم الحساب',
          'أرسل إيصال التحويل للتأكيد',
        ];
      default:
        return [];
    }
  }

  // Mock transaction for development
  static PaymentTransactionModel _createMockTransaction(
    double amount,
    PaymentMethod method,
    String currency,
    Map<String, dynamic> metadata,
  ) {
    return PaymentTransactionModel(
      transactionId: 'TXN${DateTime.now().millisecondsSinceEpoch}',
      referenceNumber: 'REF${DateTime.now().millisecondsSinceEpoch}',
      amount: amount,
      currency: currency,
      method: method,
      status: PaymentStatus.pending,
      createdAt: DateTime.now(),
      metadata: metadata,
    );
  }
}
