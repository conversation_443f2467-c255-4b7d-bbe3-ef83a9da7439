import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:travel_offers/data/cubits/location/fetch_countries_cubit.dart';
import 'package:travel_offers/data/cubits/location/fetch_states_cubit.dart';
import 'package:travel_offers/data/model/data_output.dart';
import 'package:travel_offers/data/model/location/country_model.dart';
import 'package:travel_offers/ui/theme/theme.dart';
import 'package:travel_offers/utils/app_icon.dart';
import 'package:travel_offers/utils/extensions/extensions.dart';
import 'package:travel_offers/utils/ui_utils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CountriesScreen extends StatefulWidget {
  final bool? from;
  final Map? map;

  const CountriesScreen({
    Key? key,
    this.from,
    this.map,
  }) : super(key: key);

  @override
  CountriesScreenState createState() => CountriesScreenState();
}

class CountriesScreenState extends State<CountriesScreen>
    with WidgetsBindingObserver {
  bool isFocused = false;
  String previousSearchQuery = "";
  TextEditingController searchController = TextEditingController(text: null);
  final ScrollController controller = ScrollController();
  Timer? _searchDelay;
  ValueNotifier<String> _locationStatus = ValueNotifier('disabled');
  String _currentLocation = 'خدمة الموقع غير متاحة';
  bool _isFetchingLocation = false;
  bool shouldListenToAppLifeCycle = false;

  @override
  void initState() {
    searchController.addListener(searchListener);
    context.read<FetchCountriesCubit>().fetchCountries();
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void dispose() {
    searchController.dispose();
    controller.dispose();
    _searchDelay?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void searchListener() {
    _searchDelay?.cancel();
    _searchDelay = Timer(const Duration(milliseconds: 500), () {
      if (searchController.text != previousSearchQuery) {
        if (searchController.text.isNotEmpty) {
          context
              .read<FetchCountriesCubit>()
              .fetchCountries(search: searchController.text);
        } else {
          context.read<FetchCountriesCubit>().fetchCountries();
        }
        previousSearchQuery = searchController.text;
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      shouldListenToAppLifeCycle = true;
    }
    if (state == AppLifecycleState.resumed && shouldListenToAppLifeCycle) {
      log('Location services disabled', name: 'APP LIFECYCLE');
      _locationStatus.value = 'disabled';
      shouldListenToAppLifeCycle = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.color.primaryColor,
      appBar: UiUtils.buildAppBar(context,
          title: "selectCountry".translate(context),
          actions: const [
            Text("1/4"),
            SizedBox(width: 14),
          ]),
      body: BlocBuilder<FetchCountriesCubit, FetchCountriesState>(
        builder: (context, state) {
          if (state is FetchCountriesInProgress) {
            return Center(child: UiUtils.progress());
          }
          if (state is FetchCountriesFailure) {
            return const SizedBox.shrink();
          }
          if (state is FetchCountriesSuccess) {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                  child: TextFormField(
                    controller: searchController,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide.none,
                      ),
                      fillColor: context.color.secondaryColor,
                      filled: true,
                      hintText: "searchCountry".translate(context),
                      prefixIcon: const Icon(Icons.search),
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    controller: controller,
                    itemCount: state.countries.length,
                    itemBuilder: (context, index) {
                      CountryModel country = state.countries[index];
                      return ListTile(
                        leading: Text(country.emoji ?? '🌍'),
                        title: Text(country.name ?? ''),
                        onTap: () {
                          Navigator.pop(context, country);
                        },
                      );
                    },
                  ),
                ),
              ],
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }
}
