#!/bin/bash

echo "🚀 بدء إعداد أنظمة الطيران والفعاليات الجديدة..."

# الانتقال إلى مجلد panel
cd panel

echo "🔧 إصلاح المشاكل الشائعة أولاً..."
chmod -R 755 storage bootstrap/cache
mkdir -p storage/app/public/qr_codes/tickets storage/app/public/airlines storage/app/public/events
php artisan storage:link 2>/dev/null || true

echo "📦 تشغيل migrations لتعطيل نظام الموقع الجغرافي..."
php artisan migrate --path=database/migrations/2025_01_20_disable_location_system.php

echo "✈️ تشغيل migrations لنظام الطيران..."
php artisan migrate --path=database/migrations/2025_01_20_create_flight_system.php
php artisan migrate --path=database/migrations/2025_01_20_create_flight_seats_and_bookings.php

echo "🎫 تشغيل migrations لنظام الفعاليات..."
php artisan migrate --path=database/migrations/2025_01_20_create_events_system.php
php artisan migrate --path=database/migrations/2025_01_20_create_event_dates_and_tickets.php

echo "📦 تثبيت مكتبة QR Code..."
composer require simplesoftwareio/simple-qrcode --no-interaction

echo "🌱 تشغيل seeders لإضافة البيانات التجريبية..."
php artisan db:seed --class=FlightSystemSeeder --force
php artisan db:seed --class=EventSystemSeeder --force

echo "🔄 تحديث cache التطبيق..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "🔍 فحص نهائي للأخطاء..."
composer validate --no-check-publish --no-check-all
php artisan route:list --compact | grep -E "(flights|events)" | head -10

echo "✅ تم إعداد الأنظمة الجديدة بنجاح!"
echo ""
echo "📋 ملخص التغييرات:"
echo "   ✓ تم تعطيل نظام الموقع الجغرافي نهائياً"
echo "   ✓ تم إضافة نظام الطيران الكامل"
echo "   ✓ تم إضافة نظام الفعاليات (المباريات)"
echo "   ✓ تم إضافة APIs للتطبيق المحمول"
echo "   ✓ تم إضافة بيانات تجريبية"
echo ""
echo "🔗 الروابط المهمة:"
echo "   - إدارة شركات الطيران: /admin/airlines"
echo "   - إدارة المطارات: /admin/airports"
echo "   - إدارة الرحلات: /admin/flights"
echo "   - إدارة الأماكن: /admin/venues"
echo "   - إدارة الفعاليات: /admin/events"
echo ""
echo "📱 APIs للتطبيق المحمول:"
echo "   - الرحلات: /api/flights"
echo "   - الفعاليات: /api/events"
echo ""
echo "⚠️  ملاحظات مهمة:"
echo "   - تأكد من تحديث التطبيق المحمول لدعم الأنظمة الجديدة"
echo "   - قم بإضافة الصور والشعارات للشركات والأماكن"
echo "   - اختبر جميع الوظائف قبل النشر"
