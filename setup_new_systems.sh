#!/bin/bash

echo "🚀 بدء إعداد أنظمة الطيران والفعاليات الجديدة..."

# الانتقال إلى مجلد panel
cd panel

echo "📦 تشغيل migrations لتعطيل نظام الموقع الجغرافي..."
php artisan migrate --path=database/migrations/2025_01_20_disable_location_system.php

echo "✈️ تشغيل migrations لنظام الطيران..."
php artisan migrate --path=database/migrations/2025_01_20_create_flight_system.php
php artisan migrate --path=database/migrations/2025_01_20_create_flight_seats_and_bookings.php

echo "🎫 تشغيل migrations لنظام الفعاليات..."
php artisan migrate --path=database/migrations/2025_01_20_create_events_system.php
php artisan migrate --path=database/migrations/2025_01_20_create_event_dates_and_tickets.php

echo "🌱 تشغيل seeders لإضافة البيانات التجريبية..."
php artisan db:seed --class=FlightSystemSeeder
php artisan db:seed --class=EventSystemSeeder

echo "🔄 تحديث cache التطبيق..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "✅ تم إعداد الأنظمة الجديدة بنجاح!"
echo ""
echo "📋 ملخص التغييرات:"
echo "   ✓ تم تعطيل نظام الموقع الجغرافي نهائياً"
echo "   ✓ تم إضافة نظام الطيران الكامل"
echo "   ✓ تم إضافة نظام الفعاليات (المباريات)"
echo "   ✓ تم إضافة APIs للتطبيق المحمول"
echo "   ✓ تم إضافة بيانات تجريبية"
echo ""
echo "🔗 الروابط المهمة:"
echo "   - إدارة شركات الطيران: /admin/airlines"
echo "   - إدارة المطارات: /admin/airports"
echo "   - إدارة الرحلات: /admin/flights"
echo "   - إدارة الأماكن: /admin/venues"
echo "   - إدارة الفعاليات: /admin/events"
echo ""
echo "📱 APIs للتطبيق المحمول:"
echo "   - الرحلات: /api/flights"
echo "   - الفعاليات: /api/events"
echo ""
echo "⚠️  ملاحظات مهمة:"
echo "   - تأكد من تحديث التطبيق المحمول لدعم الأنظمة الجديدة"
echo "   - قم بإضافة الصور والشعارات للشركات والأماكن"
echo "   - اختبر جميع الوظائف قبل النشر"
