<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول تواريخ الفعاليات
        Schema::create('event_dates', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('target_id')->nullable(); // event_id
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->text('ticket_types')->nullable();
            $table->tinyInteger('active')->default(0)->nullable();
            $table->text('note_to_customer')->nullable();
            $table->text('note_to_admin')->nullable();
            $table->tinyInteger('is_instant')->default(0)->nullable();
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->timestamps();
            
            $table->foreign('target_id')->references('id')->on('events')->onDelete('cascade');
        });

        // جدول أقسام التذاكر
        Schema::create('ticket_categories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('event_id');
            $table->string('name', 100)->nullable();
            $table->string('name_ar', 100)->nullable();
            $table->decimal('price', 12, 2);
            $table->integer('total_quantity');
            $table->integer('available_quantity');
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            $table->json('benefits')->nullable(); // مميزات التذكرة
            $table->string('section')->nullable(); // قسم الملعب
            $table->string('row_range')->nullable(); // نطاق الصفوف
            $table->boolean('is_vip')->default(false);
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->timestamps();
            
            $table->foreign('event_id')->references('id')->on('events')->onDelete('cascade');
        });

        // جدول حجوزات التذاكر
        Schema::create('ticket_bookings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id');
            $table->bigInteger('event_id');
            $table->bigInteger('ticket_category_id');
            $table->integer('quantity');
            $table->decimal('total_price', 12, 2);
            $table->string('booking_reference', 50)->unique();
            $table->string('qr_code', 500)->nullable(); // رابط QR Code
            $table->json('ticket_details')->nullable(); // تفاصيل التذاكر
            $table->enum('status', ['pending', 'confirmed', 'cancelled', 'used'])->default('pending');
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending');
            $table->datetime('booking_date');
            $table->datetime('used_at')->nullable(); // تاريخ استخدام التذكرة
            $table->text('special_requests')->nullable();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('event_id')->references('id')->on('events');
            $table->foreign('ticket_category_id')->references('id')->on('ticket_categories');
        });

        // جدول التذاكر الفردية (للـ QR Codes)
        Schema::create('individual_tickets', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('ticket_booking_id');
            $table->string('ticket_number')->unique();
            $table->string('qr_code')->unique();
            $table->string('seat_number')->nullable();
            $table->enum('status', ['active', 'used', 'cancelled'])->default('active');
            $table->datetime('used_at')->nullable();
            $table->bigInteger('used_by')->nullable(); // المستخدم الذي تحقق من التذكرة
            $table->timestamps();
            
            $table->foreign('ticket_booking_id')->references('id')->on('ticket_bookings')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('individual_tickets');
        Schema::dropIfExists('ticket_bookings');
        Schema::dropIfExists('ticket_categories');
        Schema::dropIfExists('event_dates');
    }
};
