class FlightModel {
  final int id;
  final String title;
  final String code;
  final String flightNumber;
  final double? reviewScore;
  final DateTime departureTime;
  final DateTime arrivalTime;
  final int duration; // in minutes
  final double minPrice;
  final AirlineModel airline;
  final AirportModel departureAirport;
  final AirportModel arrivalAirport;
  final String aircraftType;
  final String status;
  final List<FlightSeatModel> seats;

  FlightModel({
    required this.id,
    required this.title,
    required this.code,
    required this.flightNumber,
    this.reviewScore,
    required this.departureTime,
    required this.arrivalTime,
    required this.duration,
    required this.minPrice,
    required this.airline,
    required this.departureAirport,
    required this.arrivalAirport,
    required this.aircraftType,
    required this.status,
    this.seats = const [],
  });

  factory FlightModel.fromJson(Map<String, dynamic> json) {
    return FlightModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      code: json['code'] ?? '',
      flightNumber: json['flight_number'] ?? '',
      reviewScore: json['review_score']?.toDouble(),
      departureTime: DateTime.parse(json['departure_time']),
      arrivalTime: DateTime.parse(json['arrival_time']),
      duration: json['duration'] ?? 0,
      minPrice: (json['min_price'] ?? 0).toDouble(),
      airline: AirlineModel.fromJson(json['airline'] ?? {}),
      departureAirport: AirportModel.fromJson(json['departure_airport'] ?? {}),
      arrivalAirport: AirportModel.fromJson(json['arrival_airport'] ?? {}),
      aircraftType: json['aircraft_type'] ?? '',
      status: json['status'] ?? 'scheduled',
      seats: (json['seats'] as List<dynamic>?)
          ?.map((seat) => FlightSeatModel.fromJson(seat))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'code': code,
      'flight_number': flightNumber,
      'review_score': reviewScore,
      'departure_time': departureTime.toIso8601String(),
      'arrival_time': arrivalTime.toIso8601String(),
      'duration': duration,
      'min_price': minPrice,
      'airline': airline.toJson(),
      'departure_airport': departureAirport.toJson(),
      'arrival_airport': arrivalAirport.toJson(),
      'aircraft_type': aircraftType,
      'status': status,
      'seats': seats.map((seat) => seat.toJson()).toList(),
    };
  }

  String get durationFormatted {
    final hours = duration ~/ 60;
    final minutes = duration % 60;
    return '${hours}h ${minutes}m';
  }

  String get statusText {
    switch (status) {
      case 'scheduled':
        return 'مجدولة';
      case 'delayed':
        return 'متأخرة';
      case 'cancelled':
        return 'ملغية';
      case 'completed':
        return 'مكتملة';
      default:
        return 'غير محدد';
    }
  }

  bool get isBookable {
    return status == 'scheduled' && departureTime.isAfter(DateTime.now());
  }
}

class AirlineModel {
  final int id;
  final String name;
  final String code;
  final String? imageUrl;
  final String country;
  final String status;

  AirlineModel({
    required this.id,
    required this.name,
    required this.code,
    this.imageUrl,
    required this.country,
    required this.status,
  });

  factory AirlineModel.fromJson(Map<String, dynamic> json) {
    return AirlineModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      code: json['code'] ?? '',
      imageUrl: json['image_url'],
      country: json['country'] ?? '',
      status: json['status'] ?? 'publish',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'image_url': imageUrl,
      'country': country,
      'status': status,
    };
  }
}

class AirportModel {
  final int id;
  final String name;
  final String code;
  final String city;
  final String country;
  final String? timezone;
  final double? latitude;
  final double? longitude;

  AirportModel({
    required this.id,
    required this.name,
    required this.code,
    required this.city,
    required this.country,
    this.timezone,
    this.latitude,
    this.longitude,
  });

  factory AirportModel.fromJson(Map<String, dynamic> json) {
    return AirportModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      code: json['code'] ?? '',
      city: json['city'] ?? '',
      country: json['country'] ?? '',
      timezone: json['timezone'],
      latitude: json['map_lat']?.toDouble(),
      longitude: json['map_lng']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'city': city,
      'country': country,
      'timezone': timezone,
      'map_lat': latitude,
      'map_lng': longitude,
    };
  }

  String get fullName {
    return '$name ($code)';
  }

  String get cityCountry {
    return '$city, $country';
  }
}

class FlightSeatModel {
  final int id;
  final double price;
  final String seatType;
  final String? seatNumber;
  final String personType;
  final int? baggageCheckIn;
  final int? baggageCabin;
  final bool isAvailable;

  FlightSeatModel({
    required this.id,
    required this.price,
    required this.seatType,
    this.seatNumber,
    required this.personType,
    this.baggageCheckIn,
    this.baggageCabin,
    required this.isAvailable,
  });

  factory FlightSeatModel.fromJson(Map<String, dynamic> json) {
    return FlightSeatModel(
      id: json['id'] ?? 0,
      price: (json['price'] ?? 0).toDouble(),
      seatType: json['seat_type'] ?? '',
      seatNumber: json['seat_number'],
      personType: json['person'] ?? 'adult',
      baggageCheckIn: json['baggage_check_in'],
      baggageCabin: json['baggage_cabin'],
      isAvailable: json['is_available'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'price': price,
      'seat_type': seatType,
      'seat_number': seatNumber,
      'person': personType,
      'baggage_check_in': baggageCheckIn,
      'baggage_cabin': baggageCabin,
      'is_available': isAvailable,
    };
  }

  String get seatClassText {
    switch (seatType) {
      case 'economy':
        return 'اقتصادية';
      case 'business':
        return 'رجال أعمال';
      case 'first':
        return 'درجة أولى';
      case 'premium_economy':
        return 'اقتصادية مميزة';
      default:
        return 'غير محدد';
    }
  }

  String get personTypeText {
    switch (personType) {
      case 'adult':
        return 'بالغ';
      case 'child':
        return 'طفل';
      case 'infant':
        return 'رضيع';
      default:
        return 'غير محدد';
    }
  }
}
