<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول الأماكن والملاعب
        Schema::create('venues', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name', 255)->nullable();
            $table->string('name_ar', 255)->nullable();
            $table->text('address')->nullable();
            $table->string('city', 100)->nullable();
            $table->string('country', 100)->nullable();
            $table->integer('capacity')->nullable();
            $table->string('map_lat', 20)->nullable();
            $table->string('map_lng', 20)->nullable();
            $table->integer('map_zoom')->nullable();
            $table->json('facilities')->nullable(); // مرافق المكان
            $table->json('images')->nullable(); // صور المكان
            $table->string('status', 30)->nullable()->default('active');
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->timestamps();
        });

        // جدول الفعاليات والمباريات
        Schema::create('events', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('title', 255)->nullable();
            $table->string('title_ar', 255)->nullable();
            $table->string('slug', 255)->charset('utf8')->index();
            $table->text('content')->nullable();
            $table->text('content_ar')->nullable();
            $table->bigInteger('image_id')->nullable();
            $table->bigInteger('banner_image_id')->nullable();
            $table->bigInteger('venue_id')->nullable();
            $table->string('address', 255)->nullable();
            $table->string('map_lat', 20)->nullable();
            $table->string('map_lng', 20)->nullable();
            $table->integer('map_zoom')->nullable();
            $table->tinyInteger('is_featured')->nullable();
            $table->string('gallery', 255)->nullable();
            $table->string('video', 255)->nullable();
            $table->text('faqs')->nullable();
            $table->text('ticket_types')->nullable();
            $table->integer('duration')->nullable(); // بالدقائق
            $table->string('start_time')->nullable();
            $table->enum('event_type', ['football', 'basketball', 'concert', 'conference', 'other'])->default('football');
            $table->string('organizer')->nullable();
            
            // الأسعار
            $table->decimal('price', 12, 2)->nullable();
            $table->decimal('sale_price', 12, 2)->nullable();
            $table->tinyInteger('is_instant')->default(0)->nullable();
            $table->tinyInteger('enable_extra_price')->nullable();
            $table->text('extra_price')->nullable();
            $table->decimal('review_score', 2, 1)->nullable();
            
            $table->string('ical_import_url')->nullable();
            $table->string('status', 50)->nullable()->default('upcoming');
            $table->tinyInteger('default_state')->default(1)->nullable();
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->softDeletes();
            $table->timestamps();
            
            $table->foreign('venue_id')->references('id')->on('venues');
        });

        // جدول ترجمات الفعاليات
        Schema::create('event_translations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('origin_id')->unsigned();
            $table->string('locale')->index();
            $table->string('title', 255)->nullable();
            $table->text('content')->nullable();
            $table->text('faqs')->nullable();
            $table->string('address', 255)->nullable();
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        // جدول شروط الفعاليات
        Schema::create('event_terms', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('term_id')->nullable();
            $table->bigInteger('target_id')->nullable();
            $table->bigInteger('create_user')->nullable();
            $table->bigInteger('update_user')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_terms');
        Schema::dropIfExists('event_translations');
        Schema::dropIfExists('events');
        Schema::dropIfExists('venues');
    }
};
