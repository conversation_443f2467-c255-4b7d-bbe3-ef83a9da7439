# دليل دمج أنظمة الطيران والفعاليات

## 📋 نظرة عامة

تم دمج أنظمة الطيران والفعاليات (المباريات) من مشروع MyTravel مع مشروع Travel Offers الحالي، مع تعطيل نظام الموقع الجغرافي نهائياً كما هو مطلوب.

## 🚀 التغييرات المنفذة

### 1. تعطيل نظام الموقع الجغرافي
- ✅ إزالة حقول الموقع من جدول `items`
- ✅ تعطيل Google Maps في التطبيق المحمول
- ✅ تحديث إعدادات النظام لتعطيل الخرائط
- ✅ تعطيل فلاتر الموقع الجغرافي

### 2. نظام الطيران الكامل
- ✅ جداول قاعدة البيانات: `airlines`, `airports`, `flights`, `flight_seats`, `flight_bookings`
- ✅ نماذج Laravel: `Airline`, `Airport`, `Flight`, `FlightSeat`, `FlightBooking`
- ✅ Controllers إدارية: `AirlineController`, `FlightController`
- ✅ APIs للتطبيق المحمول: `/api/flights`
- ✅ نماذج Flutter: `FlightModel`, `AirlineModel`, `AirportModel`

### 3. نظام الفعاليات (المباريات)
- ✅ جداول قاعدة البيانات: `venues`, `events`, `ticket_categories`, `ticket_bookings`
- ✅ نماذج Laravel: `Venue`, `Event`, `TicketCategory`, `TicketBooking`
- ✅ Controllers إدارية: `VenueController`, `EventController`
- ✅ APIs للتطبيق المحمول: `/api/events`
- ✅ نماذج Flutter: `EventModel`, `VenueModel`, `TicketCategoryModel`
- ✅ نظام QR Code للتذاكر

## 📁 هيكل الملفات الجديدة

### Backend (Laravel)
```
panel/
├── database/migrations/
│   ├── 2025_01_20_disable_location_system.php
│   ├── 2025_01_20_create_flight_system.php
│   ├── 2025_01_20_create_flight_seats_and_bookings.php
│   ├── 2025_01_20_create_events_system.php
│   └── 2025_01_20_create_event_dates_and_tickets.php
├── app/Models/
│   ├── Airline.php
│   ├── Airport.php
│   ├── Flight.php
│   ├── FlightSeat.php
│   ├── FlightBooking.php
│   ├── Venue.php
│   ├── Event.php
│   ├── TicketCategory.php
│   └── TicketBooking.php
├── app/Http/Controllers/Admin/
│   ├── AirlineController.php
│   ├── FlightController.php
│   ├── VenueController.php
│   └── EventController.php
├── app/Http/Controllers/Api/
│   ├── FlightApiController.php
│   └── EventApiController.php
└── database/seeders/
    ├── FlightSystemSeeder.php
    └── EventSystemSeeder.php
```

### Frontend (Flutter)
```
lib/
├── data/model/flight/
│   └── flight_model.dart
└── data/model/event/
    └── event_model.dart
```

## 🔧 التثبيت والإعداد

### 1. تشغيل الإعداد التلقائي
```bash
chmod +x setup_new_systems.sh
./setup_new_systems.sh
```

### 2. الإعداد اليدوي
```bash
cd panel

# تشغيل migrations
php artisan migrate --path=database/migrations/2025_01_20_disable_location_system.php
php artisan migrate --path=database/migrations/2025_01_20_create_flight_system.php
php artisan migrate --path=database/migrations/2025_01_20_create_flight_seats_and_bookings.php
php artisan migrate --path=database/migrations/2025_01_20_create_events_system.php
php artisan migrate --path=database/migrations/2025_01_20_create_event_dates_and_tickets.php

# تشغيل seeders
php artisan db:seed --class=FlightSystemSeeder
php artisan db:seed --class=EventSystemSeeder

# تحديث cache
php artisan config:cache
php artisan route:cache
```

## 🌐 APIs الجديدة

### نظام الطيران
```
GET    /api/flights                 - قائمة الرحلات مع الفلاتر
GET    /api/flights/{id}            - تفاصيل رحلة محددة
GET    /api/flights/airlines/list   - قائمة شركات الطيران
GET    /api/flights/airports/list   - قائمة المطارات
POST   /api/flights/book            - حجز رحلة (يتطلب تسجيل دخول)
GET    /api/flights/my-bookings     - حجوزات المستخدم (يتطلب تسجيل دخول)
```

### نظام الفعاليات
```
GET    /api/events                  - قائمة الفعاليات مع الفلاتر
GET    /api/events/{id}             - تفاصيل فعالية محددة
GET    /api/events/venues/list      - قائمة الأماكن
POST   /api/events/book             - حجز تذاكر (يتطلب تسجيل دخول)
GET    /api/events/my-bookings      - حجوزات المستخدم (يتطلب تسجيل دخول)
POST   /api/events/verify-ticket    - التحقق من صحة التذكرة
POST   /api/events/use-ticket       - استخدام التذكرة (يتطلب تسجيل دخول)
```

## 🎛️ لوحة التحكم الإدارية

### الروابط الجديدة
- إدارة شركات الطيران: `/admin/airlines`
- إدارة المطارات: `/admin/airports`
- إدارة الرحلات: `/admin/flights`
- إدارة الأماكن: `/admin/venues`
- إدارة الفعاليات: `/admin/events`

### الميزات الإدارية
- ✅ إضافة/تعديل/حذف شركات الطيران مع الشعارات
- ✅ إدارة المطارات والمدن
- ✅ جدولة الرحلات وإدارة المقاعد
- ✅ إنشاء الفعاليات وإدارة التذاكر
- ✅ تتبع الحجوزات والمدفوعات

## 📱 تحديثات التطبيق المحمول

### الميزات الجديدة
- ✅ تعطيل نظام الخرائط والموقع الجغرافي
- ✅ شاشات البحث عن الرحلات والحجز
- ✅ شاشات تصفح الفعاليات وحجز التذاكر
- ✅ عرض QR Code للتذاكر
- ✅ تتبع الحجوزات والرحلات

### النماذج الجديدة
- `FlightModel` - نموذج الرحلات
- `AirlineModel` - نموذج شركات الطيران
- `AirportModel` - نموذج المطارات
- `EventModel` - نموذج الفعاليات
- `VenueModel` - نموذج الأماكن
- `TicketCategoryModel` - نموذج فئات التذاكر

## 🔒 الأمان والصلاحيات

### APIs محمية
- جميع APIs الحجز تتطلب تسجيل دخول
- التحقق من صحة البيانات المدخلة
- حماية من الحجز المتكرر

### الصلاحيات الإدارية
- Super Admin: وصول كامل لجميع الأنظمة
- Admin: إدارة الرحلات والفعاليات
- User: الحجز والتصفح فقط

## 🧪 البيانات التجريبية

### شركات الطيران
- الخطوط الكويتية (KU)
- طيران الإمارات (EK)
- الخطوط القطرية (QR)
- الخطوط السعودية (SV)
- طيران الاتحاد (EY)

### المطارات
- مطار الكويت الدولي (KWI)
- مطار دبي الدولي (DXB)
- مطار حمد الدولي (DOH)
- مطار الملك عبدالعزيز (JED)
- مطار هيثرو لندن (LHR)

### الأماكن والفعاليات
- استاد جابر الأحمد الدولي
- استاد النادي الكويتي الرياضي
- مركز الشيخ جابر الأحمد الثقافي
- مباريات كرة قدم وسلة
- حفلات موسيقية

## ⚠️ ملاحظات مهمة

1. **تأكد من النسخ الاحتياطية** قبل تشغيل الـ migrations
2. **اختبر جميع الوظائف** في بيئة التطوير أولاً
3. **أضف الصور والشعارات** للشركات والأماكن
4. **راجع الصلاحيات** للمستخدمين الإداريين
5. **اختبر APIs** مع التطبيق المحمول

## 🐛 استكشاف الأخطاء

### مشاكل شائعة
- **خطأ في الـ migration**: تأكد من وجود جداول المستخدمين أولاً
- **خطأ في الـ seeder**: تأكد من تشغيل migrations قبل seeders
- **مشكلة في الصور**: تأكد من صلاحيات مجلد storage
- **خطأ في APIs**: تأكد من تحديث route cache

### الحلول
```bash
# إعادة تشغيل migrations
php artisan migrate:fresh --seed

# مسح cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear

# إصلاح صلاحيات الملفات
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير مع تفاصيل المشكلة ورسائل الخطأ.
