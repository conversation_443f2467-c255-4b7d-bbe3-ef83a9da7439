import 'package:flutter/material.dart';
import 'package:travel_offers/data/model/event/event_model.dart';
import 'package:travel_offers/ui/widgets/event_card.dart';

class EventsScreen extends StatefulWidget {
  const EventsScreen({Key? key}) : super(key: key);

  @override
  State<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends State<EventsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<EventModel> events = [];
  bool isLoading = true;
  String selectedEventType = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadEvents();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadEvents() async {
    setState(() {
      isLoading = true;
    });

    try {
      // : Implement API call to load events
      // final response = await EventService.getEvents(filters);
      
      // Mock data for now
      await Future.delayed(Duration(seconds: 2));
      setState(() {
        events = _getMockEvents();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل الفعاليات')),
      );
    }
  }

  List<EventModel> _getMockEvents() {
    return [
      EventModel(
        id: 1,
        title: 'Kuwait vs Saudi Arabia - World Cup Qualifier',
        titleAr: 'الكويت ضد السعودية - تصفيات كأس العالم',
        slug: 'kuwait-vs-saudi-arabia',
        content: 'Exciting World Cup qualifier match',
        contentAr: 'مباراة مثيرة في تصفيات كأس العالم',
        venue: VenueModel(
          id: 1,
          name: 'Jaber Al-Ahmad International Stadium',
          nameAr: 'استاد جابر الأحمد الدولي',
          address: 'South Surra, Kuwait City',
          city: 'Kuwait City',
          country: 'Kuwait',
          capacity: 60000,
        ),
        eventType: 'football',
        organizer: 'Kuwait Football Association',
        duration: 120,
        startTime: '20:00',
        price: 25.0,
        salePrice: 20.0,
        isFeatured: true,
        isInstant: false,
        reviewScore: 4.5,
        status: 'upcoming',
        ticketCategories: [
          TicketCategoryModel(
            id: 1,
            name: 'General Admission',
            nameAr: 'دخول عام',
            price: 20.0,
            totalQuantity: 30000,
            availableQuantity: 25000,
            isVip: false,
          ),
          TicketCategoryModel(
            id: 2,
            name: 'VIP Seats',
            nameAr: 'مقاعد VIP',
            price: 60.0,
            totalQuantity: 2000,
            availableQuantity: 1500,
            isVip: true,
          ),
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('الفعاليات والمباريات'),
        backgroundColor: Theme.of(context).primaryColor,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(text: 'الكل'),
            Tab(text: 'كرة القدم'),
            Tab(text: 'كرة السلة'),
            Tab(text: 'حفلات'),
            Tab(text: 'مؤتمرات'),
          ],
          onTap: (index) {
            setState(() {
              selectedEventType = _getEventTypeFromIndex(index);
            });
            _loadEvents();
          },
        ),
      ),
      body: Column(
        children: [
          // Filter Section
          Container(
            padding: EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'البحث في الفعاليات...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onChanged: (value) {
                      // : Implement search functionality
                    },
                  ),
                ),
                SizedBox(width: 16),
                IconButton(
                  onPressed: () {
                    // : Show filter dialog
                    _showFilterDialog();
                  },
                  icon: Icon(Icons.filter_list),
                  style: IconButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          
          // Events List
          Expanded(
            child: isLoading
                ? Center(child: CircularProgressIndicator())
                : events.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.event, size: 64, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد فعاليات متاحة',
                              style: TextStyle(fontSize: 18, color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: EdgeInsets.all(16),
                        itemCount: events.length,
                        itemBuilder: (context, index) {
                          return EventCard(
                            event: events[index],
                            onTap: () => _navigateToEventDetails(events[index]),
                          );
                        },
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // : Navigate to my bookings
          Navigator.pushNamed(context, '/my-event-bookings');
        },
        child: Icon(Icons.confirmation_number),
        tooltip: 'حجوزاتي',
      ),
    );
  }

  String _getEventTypeFromIndex(int index) {
    switch (index) {
      case 0: return 'all';
      case 1: return 'football';
      case 2: return 'basketball';
      case 3: return 'concert';
      case 4: return 'conference';
      default: return 'all';
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('فلترة الفعاليات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text('الفعاليات المميزة فقط'),
              trailing: Switch(
                value: false,
                onChanged: (value) {
                  // : Implement featured filter
                },
              ),
            ),
            ListTile(
              title: Text('الفعاليات المتاحة فقط'),
              trailing: Switch(
                value: true,
                onChanged: (value) {
                  // : Implement available filter
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _loadEvents();
            },
            child: Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _navigateToEventDetails(EventModel event) {
    Navigator.pushNamed(
      context,
      '/event-details',
      arguments: event,
    );
  }
}
