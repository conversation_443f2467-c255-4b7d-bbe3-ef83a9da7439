@extends('layouts.admin')

@section('title', 'إضافة فعالية جديدة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus"></i>
                        إضافة فعالية جديدة
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.events.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>

                <form action="{{ route('admin.events.store') }}" method="POST" enctype="multipart/form-data" id="event-form">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <!-- معلومات الفعالية الأساسية -->
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>معلومات الفعالية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="title">عنوان الفعالية <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title') }}" 
                                                   placeholder="مثال: مباراة الكويت ضد السعودية" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="description">وصف الفعالية <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="4" 
                                                      placeholder="وصف تفصيلي للفعالية..." required>{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="venue_id">المكان <span class="text-danger">*</span></label>
                                                    <select class="form-control select2 @error('venue_id') is-invalid @enderror" 
                                                            id="venue_id" name="venue_id" required>
                                                        <option value="">اختر المكان</option>
                                                        @foreach($venues as $venue)
                                                            <option value="{{ $venue->id }}" {{ old('venue_id') == $venue->id ? 'selected' : '' }}>
                                                                {{ $venue->name }} - {{ $venue->city }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('venue_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="category_id">الفئة</label>
                                                    <select class="form-control select2 @error('category_id') is-invalid @enderror" 
                                                            id="category_id" name="category_id">
                                                        <option value="">اختر الفئة</option>
                                                        @foreach($categories as $category)
                                                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                                {{ $category->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('category_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="image">صورة الفعالية</label>
                                            <input type="file" class="form-control-file @error('image') is-invalid @enderror" 
                                                   id="image" name="image" accept="image/*">
                                            @error('image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">الحد الأقصى: 2MB، الأنواع المدعومة: JPG, PNG, GIF</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- تواريخ الفعالية -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5>تواريخ الفعالية</h5>
                                        <button type="button" class="btn btn-sm btn-primary float-right" onclick="addEventDate()">
                                            <i class="fas fa-plus"></i>
                                            إضافة تاريخ
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="event-dates">
                                            <div class="event-date-row">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label>تاريخ الفعالية <span class="text-danger">*</span></label>
                                                            <input type="date" class="form-control" name="event_dates[0][date]" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label>وقت البداية <span class="text-danger">*</span></label>
                                                            <input type="time" class="form-control" name="event_dates[0][start_time]" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label>وقت النهاية</label>
                                                            <input type="time" class="form-control" name="event_dates[0][end_time]">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label>&nbsp;</label>
                                                            <button type="button" class="btn btn-danger btn-block" onclick="removeEventDate(this)" disabled>
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الإعدادات الجانبية -->
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>إعدادات الفعالية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="status">حالة الفعالية</label>
                                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status">
                                                <option value="upcoming" {{ old('status') == 'upcoming' ? 'selected' : '' }}>قادمة</option>
                                                <option value="ongoing" {{ old('status') == 'ongoing' ? 'selected' : '' }}>جارية</option>
                                                <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>مكتملة</option>
                                                <option value="cancelled" {{ old('status') == 'cancelled' ? 'selected' : '' }}>ملغية</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" 
                                                       value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_featured">
                                                    فعالية مميزة
                                                </label>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                                       value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">
                                                    فعالية نشطة
                                                </label>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="max_tickets_per_user">الحد الأقصى للتذاكر لكل مستخدم</label>
                                            <input type="number" class="form-control @error('max_tickets_per_user') is-invalid @enderror" 
                                                   id="max_tickets_per_user" name="max_tickets_per_user" 
                                                   value="{{ old('max_tickets_per_user', 10) }}" min="1">
                                            @error('max_tickets_per_user')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="booking_start_date">بداية الحجز</label>
                                            <input type="datetime-local" class="form-control @error('booking_start_date') is-invalid @enderror" 
                                                   id="booking_start_date" name="booking_start_date" value="{{ old('booking_start_date') }}">
                                            @error('booking_start_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="booking_end_date">نهاية الحجز</label>
                                            <input type="datetime-local" class="form-control @error('booking_end_date') is-invalid @enderror" 
                                                   id="booking_end_date" name="booking_end_date" value="{{ old('booking_end_date') }}">
                                            @error('booking_end_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات إضافية -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5>معلومات إضافية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="organizer">المنظم</label>
                                            <input type="text" class="form-control @error('organizer') is-invalid @enderror" 
                                                   id="organizer" name="organizer" value="{{ old('organizer') }}" 
                                                   placeholder="اسم المنظم">
                                            @error('organizer')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="contact_info">معلومات الاتصال</label>
                                            <textarea class="form-control @error('contact_info') is-invalid @enderror" 
                                                      id="contact_info" name="contact_info" rows="3" 
                                                      placeholder="رقم الهاتف، البريد الإلكتروني...">{{ old('contact_info') }}</textarea>
                                            @error('contact_info')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="terms_conditions">الشروط والأحكام</label>
                                            <textarea class="form-control @error('terms_conditions') is-invalid @enderror" 
                                                      id="terms_conditions" name="terms_conditions" rows="4" 
                                                      placeholder="شروط وأحكام الفعالية...">{{ old('terms_conditions') }}</textarea>
                                            @error('terms_conditions')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الفعالية
                        </button>
                        <button type="button" class="btn btn-success" id="save-and-add-tickets">
                            <i class="fas fa-ticket-alt"></i>
                            حفظ وإضافة التذاكر
                        </button>
                        <a href="{{ route('admin.events.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let eventDateIndex = 1;

$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    // Save and add tickets button
    $('#save-and-add-tickets').on('click', function() {
        $('#event-form').append('<input type="hidden" name="redirect_to_tickets" value="1">');
        $('#event-form').submit();
    });
});

function addEventDate() {
    const eventDatesContainer = document.getElementById('event-dates');
    const newDateRow = document.createElement('div');
    newDateRow.className = 'event-date-row';
    newDateRow.innerHTML = `
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label>تاريخ الفعالية <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" name="event_dates[${eventDateIndex}][date]" required>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>وقت البداية <span class="text-danger">*</span></label>
                    <input type="time" class="form-control" name="event_dates[${eventDateIndex}][start_time]" required>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>وقت النهاية</label>
                    <input type="time" class="form-control" name="event_dates[${eventDateIndex}][end_time]">
                </div>
            </div>
            <div class="col-md-2">
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button type="button" class="btn btn-danger btn-block" onclick="removeEventDate(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    eventDatesContainer.appendChild(newDateRow);
    eventDateIndex++;
    
    // Enable remove buttons
    updateRemoveButtons();
}

function removeEventDate(button) {
    const eventDateRow = button.closest('.event-date-row');
    eventDateRow.remove();
    updateRemoveButtons();
}

function updateRemoveButtons() {
    const removeButtons = document.querySelectorAll('.event-date-row button[onclick*="removeEventDate"]');
    removeButtons.forEach((button, index) => {
        button.disabled = removeButtons.length === 1;
    });
}

// Form validation
$('#event-form').on('submit', function(e) {
    const eventDates = document.querySelectorAll('.event-date-row');
    if (eventDates.length === 0) {
        alert('يجب إضافة تاريخ واحد على الأقل للفعالية');
        e.preventDefault();
        return false;
    }
    
    // Validate booking dates
    const bookingStart = $('#booking_start_date').val();
    const bookingEnd = $('#booking_end_date').val();
    
    if (bookingStart && bookingEnd && new Date(bookingStart) >= new Date(bookingEnd)) {
        alert('تاريخ نهاية الحجز يجب أن يكون بعد تاريخ البداية');
        e.preventDefault();
        return false;
    }
});
</script>
@endsection
