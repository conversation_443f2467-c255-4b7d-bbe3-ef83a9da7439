import 'dart:convert';
import 'package:travel_offers/data/services/cache_service.dart';

enum NotificationType {
  flightReminder,
  eventReminder,
  paymentConfirmation,
  weatherAlert,
  prayerTime,
  holiday,
  promotion,
  general,
}

class NotificationModel {
  final String id;
  final NotificationType type;
  final String title;
  final String titleAr;
  final String body;
  final String bodyAr;
  final DateTime scheduledTime;
  final Map<String, dynamic>? payload;
  final bool isRead;
  final DateTime createdAt;

  NotificationModel({
    required this.id,
    required this.type,
    required this.title,
    required this.titleAr,
    required this.body,
    required this.bodyAr,
    required this.scheduledTime,
    this.payload,
    this.isRead = false,
    required this.createdAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      type: NotificationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => NotificationType.general,
      ),
      title: json['title'],
      titleAr: json['titleAr'],
      body: json['body'],
      bodyAr: json['bodyAr'],
      scheduledTime: DateTime.parse(json['scheduledTime']),
      payload: json['payload'],
      isRead: json['isRead'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'title': title,
      'titleAr': titleAr,
      'body': body,
      'bodyAr': bodyAr,
      'scheduledTime': scheduledTime.toIso8601String(),
      'payload': payload,
      'isRead': isRead,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  NotificationModel copyWith({
    String? id,
    NotificationType? type,
    String? title,
    String? titleAr,
    String? body,
    String? bodyAr,
    DateTime? scheduledTime,
    Map<String, dynamic>? payload,
    bool? isRead,
    DateTime? createdAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      titleAr: titleAr ?? this.titleAr,
      body: body ?? this.body,
      bodyAr: bodyAr ?? this.bodyAr,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      payload: payload ?? this.payload,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  String get displayTitle {
    final isArabic = CacheService.getLanguage() == 'ar';
    return isArabic ? titleAr : title;
  }

  String get displayBody {
    final isArabic = CacheService.getLanguage() == 'ar';
    return isArabic ? bodyAr : body;
  }
}

class NotificationService {
  static const String _notificationsKey = 'app_notifications';
  static bool _initialized = false;

  static Future<void> init() async {
    if (_initialized) return;
    // Initialize notification service (simplified version)
    _initialized = true;
  }

  static Future<bool> requestPermissions() async {
    // In a real implementation, this would request actual permissions
    return true;
  }

  static Future<void> scheduleNotification(NotificationModel notification) async {
    if (!CacheService.getNotificationsEnabled()) return;

    // In a real implementation, this would schedule actual notifications
    // For now, just store the notification locally
    await _storeNotification(notification);
  }

  static Future<void> showImmediateNotification(NotificationModel notification) async {
    if (!CacheService.getNotificationsEnabled()) return;

    // In a real implementation, this would show actual notifications
    // For now, just store the notification locally
    await _storeNotification(notification.copyWith(
      scheduledTime: DateTime.now(),
    ));
  }

  static Future<void> cancelNotification(String id) async {
    // In a real implementation, this would cancel actual notifications
    await _removeStoredNotification(id);
  }

  static Future<void> cancelAllNotifications() async {
    // In a real implementation, this would cancel all notifications
    await _clearStoredNotifications();
  }

  // Specific notification types
  static Future<void> scheduleFlightReminder({
    required String flightNumber,
    required DateTime departureTime,
    required String from,
    required String to,
    Map<String, dynamic>? payload,
  }) async {
    final reminderTime = departureTime.subtract(Duration(hours: 2));
    
    final notification = NotificationModel(
      id: 'flight_${flightNumber}_reminder',
      type: NotificationType.flightReminder,
      title: 'Flight Reminder',
      titleAr: 'تذكير الرحلة',
      body: 'Your flight $flightNumber from $from to $to departs in 2 hours',
      bodyAr: 'رحلتك $flightNumber من $from إلى $to تغادر خلال ساعتين',
      scheduledTime: reminderTime,
      payload: payload,
      createdAt: DateTime.now(),
    );

    await scheduleNotification(notification);
  }

  static Future<void> scheduleEventReminder({
    required String eventTitle,
    required DateTime eventTime,
    required String venue,
    Map<String, dynamic>? payload,
  }) async {
    final reminderTime = eventTime.subtract(Duration(hours: 1));
    
    final notification = NotificationModel(
      id: 'event_${eventTitle.hashCode}_reminder',
      type: NotificationType.eventReminder,
      title: 'Event Reminder',
      titleAr: 'تذكير الفعالية',
      body: '$eventTitle at $venue starts in 1 hour',
      bodyAr: '$eventTitle في $venue تبدأ خلال ساعة',
      scheduledTime: reminderTime,
      payload: payload,
      createdAt: DateTime.now(),
    );

    await scheduleNotification(notification);
  }

  static Future<void> showPaymentConfirmation({
    required String bookingReference,
    required double amount,
    required String currency,
    Map<String, dynamic>? payload,
  }) async {
    final notification = NotificationModel(
      id: 'payment_${bookingReference}',
      type: NotificationType.paymentConfirmation,
      title: 'Payment Confirmed',
      titleAr: 'تم تأكيد الدفع',
      body: 'Payment of $amount $currency confirmed for booking $bookingReference',
      bodyAr: 'تم تأكيد دفع $amount $currency للحجز $bookingReference',
      scheduledTime: DateTime.now(),
      payload: payload,
      createdAt: DateTime.now(),
    );

    await showImmediateNotification(notification);
  }

  static Future<void> showWeatherAlert({
    required String alertType,
    required String message,
    required String messageAr,
    Map<String, dynamic>? payload,
  }) async {
    final notification = NotificationModel(
      id: 'weather_${alertType}_${DateTime.now().millisecondsSinceEpoch}',
      type: NotificationType.weatherAlert,
      title: 'Weather Alert',
      titleAr: 'تنبيه طقس',
      body: message,
      bodyAr: messageAr,
      scheduledTime: DateTime.now(),
      payload: payload,
      createdAt: DateTime.now(),
    );

    await showImmediateNotification(notification);
  }

  static Future<void> schedulePrayerReminder({
    required String prayerName,
    required String prayerNameAr,
    required DateTime prayerTime,
    Map<String, dynamic>? payload,
  }) async {
    final reminderTime = prayerTime.subtract(Duration(minutes: 10));
    
    final notification = NotificationModel(
      id: 'prayer_${prayerName}_${prayerTime.day}',
      type: NotificationType.prayerTime,
      title: 'Prayer Time',
      titleAr: 'وقت الصلاة',
      body: '$prayerName prayer time in 10 minutes',
      bodyAr: 'صلاة $prayerNameAr خلال 10 دقائق',
      scheduledTime: reminderTime,
      payload: payload,
      createdAt: DateTime.now(),
    );

    await scheduleNotification(notification);
  }

  static Future<void> showHolidayNotification({
    required String holidayName,
    required String holidayNameAr,
    required DateTime holidayDate,
    Map<String, dynamic>? payload,
  }) async {
    final notification = NotificationModel(
      id: 'holiday_${holidayName.hashCode}',
      type: NotificationType.holiday,
      title: 'Holiday Reminder',
      titleAr: 'تذكير المناسبة',
      body: '$holidayName is today!',
      bodyAr: '$holidayNameAr اليوم!',
      scheduledTime: DateTime.now(),
      payload: payload,
      createdAt: DateTime.now(),
    );

    await showImmediateNotification(notification);
  }

  // Notification management
  static List<NotificationModel> getStoredNotifications() {
    try {
      final notificationsString = CacheService.prefs.getString(_notificationsKey);
      if (notificationsString == null) return [];
      
      final notificationsList = json.decode(notificationsString) as List<dynamic>;
      return notificationsList
          .map((json) => NotificationModel.fromJson(json))
          .toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    } catch (e) {
      return [];
    }
  }

  static Future<void> markAsRead(String id) async {
    final notifications = getStoredNotifications();
    final index = notifications.indexWhere((n) => n.id == id);
    
    if (index != -1) {
      notifications[index] = notifications[index].copyWith(isRead: true);
      await _saveNotifications(notifications);
    }
  }

  static Future<void> markAllAsRead() async {
    final notifications = getStoredNotifications()
        .map((n) => n.copyWith(isRead: true))
        .toList();
    await _saveNotifications(notifications);
  }

  static int getUnreadCount() {
    return getStoredNotifications().where((n) => !n.isRead).length;
  }

  static Future<void> deleteNotification(String id) async {
    await cancelNotification(id);
  }

  static Future<void> deleteAllNotifications() async {
    await cancelAllNotifications();
  }

  // Private methods - removed unused methods to clean up warnings

  static Future<void> _storeNotification(NotificationModel notification) async {
    final notifications = getStoredNotifications();
    
    // Remove existing notification with same ID
    notifications.removeWhere((n) => n.id == notification.id);
    
    // Add new notification
    notifications.insert(0, notification);
    
    // Keep only last 50 notifications
    if (notifications.length > 50) {
      notifications.removeRange(50, notifications.length);
    }
    
    await _saveNotifications(notifications);
  }

  static Future<void> _removeStoredNotification(String id) async {
    final notifications = getStoredNotifications();
    notifications.removeWhere((n) => n.id == id);
    await _saveNotifications(notifications);
  }

  static Future<void> _clearStoredNotifications() async {
    await CacheService.prefs.remove(_notificationsKey);
  }

  static Future<void> _saveNotifications(List<NotificationModel> notifications) async {
    final notificationsJson = notifications.map((n) => n.toJson()).toList();
    await CacheService.prefs.setString(_notificationsKey, json.encode(notificationsJson));
  }
}
